<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use App\Filament\Traits\HandlesImageStorage;
use App\Helpers\ImageStorage;

class GenerateThumbnails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'thumbnails:generate {--type=all : Type de contenu (products, categories, banners, reviews, all)} {--force : Régénérer les miniatures existantes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Génère les miniatures pour les images existantes';

    /**
     * Tailles de miniatures par type de contenu (copié du trait)
     */
    protected $thumbnailSizes = [
        'products' => [
            'small' => [150, 150],
            'medium' => [300, 300],
            'large' => [600, 600]
        ],
        'categories' => [
            'small' => [100, 100],
            'medium' => [200, 200],
            'large' => [400, 400]
        ],
        'banners' => [
            'small' => [300, 128],
            'medium' => [600, 257],
            'large' => [1200, 514]
        ],
        'reviews' => [
            'small' => [100, 100],
            'medium' => [200, 200],
            'large' => [400, 400]
        ]
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Vérifier si Intervention Image est disponible
        if (!class_exists('Intervention\Image\Facades\Image')) {
            $this->error('Intervention Image n\'est pas installé.');
            $this->info('Veuillez installer Intervention Image avec la commande :');
            $this->line('composer require intervention/image');
            return 1;
        }

        $type = $this->option('type');
        $force = $this->option('force');

        $this->info('Génération des miniatures...');

        if ($type === 'all') {
            foreach (array_keys($this->thumbnailSizes) as $contentType) {
                $this->generateThumbnailsForType($contentType, $force);
            }
        } else {
            if (!isset($this->thumbnailSizes[$type])) {
                $this->error("Type de contenu invalide: {$type}");
                $this->info("Types disponibles: " . implode(', ', array_keys($this->thumbnailSizes)));
                return 1;
            }
            $this->generateThumbnailsForType($type, $force);
        }

        $this->info('Génération des miniatures terminée !');
        return 0;
    }

    /**
     * Génère les miniatures pour un type de contenu spécifique
     */
    protected function generateThumbnailsForType(string $type, bool $force = false)
    {
        $this->info("Traitement des images de type: {$type}");

        $imagesPath = public_path("images/{$type}");

        if (!File::exists($imagesPath)) {
            $this->warn("Le dossier {$imagesPath} n'existe pas.");
            return;
        }

        // Parcourir tous les dossiers dans le type
        $folders = File::directories($imagesPath);

        if (empty($folders)) {
            $this->warn("Aucun dossier trouvé dans {$imagesPath}");
            return;
        }

        $totalImages = 0;
        $processedImages = 0;

        foreach ($folders as $folder) {
            $folderName = basename($folder);

            // Ignorer le dossier thumbnail s'il existe
            if ($folderName === 'thumbnail') {
                continue;
            }

            $images = File::files($folder);
            $totalImages += count($images);

            foreach ($images as $image) {
                if ($this->isImageFile($image->getPathname())) {
                    $this->generateThumbnailsForImage($image->getPathname(), $type, $folderName, $image->getFilename(), $force);
                    $processedImages++;
                }
            }
        }

        $this->info("Type {$type}: {$processedImages} images traitées sur {$totalImages} fichiers trouvés.");
    }

    /**
     * Génère les miniatures pour une image spécifique
     */
    protected function generateThumbnailsForImage(string $imagePath, string $baseDir, string $folderPrefix, string $filename, bool $force = false)
    {
        $sizes = $this->thumbnailSizes[$baseDir];

        foreach ($sizes as $sizeName => $dimensions) {
            // Créer le dossier de miniatures s'il n'existe pas
            $thumbnailDir = public_path("images/thumbnail/{$baseDir}/{$folderPrefix}/{$sizeName}");
            if (!File::exists($thumbnailDir)) {
                File::makeDirectory($thumbnailDir, 0755, true);
            }

            // Vérifier si la miniature existe déjà
            $thumbnailPath = $thumbnailDir . '/' . $filename;
            if (!$force && File::exists($thumbnailPath)) {
                continue; // Passer si la miniature existe déjà et force n'est pas activé
            }

            try {
                $img = \Intervention\Image\Facades\Image::make($imagePath);

                // Redimensionner l'image en conservant les proportions
                $img->resize($dimensions[0], $dimensions[1], function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

                // Sauvegarder la miniature
                $img->save($thumbnailPath, 85); // Qualité 85%

                $this->line("✓ Miniature générée: {$baseDir}/{$folderPrefix}/{$sizeName}/{$filename}");
            } catch (\Exception $e) {
                $this->error("✗ Erreur pour {$filename}: " . $e->getMessage());
            }
        }
    }

    /**
     * Vérifie si un fichier est une image
     */
    protected function isImageFile(string $filePath): bool
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        return in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp']);
    }
}
