<?php

namespace App\Filament\Resources\ZoneLivraisonResource\Pages;

use App\Filament\Resources\ZoneLivraisonResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListZoneLivraisons extends ListRecords
{
    protected static string $resource = ZoneLivraisonResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
