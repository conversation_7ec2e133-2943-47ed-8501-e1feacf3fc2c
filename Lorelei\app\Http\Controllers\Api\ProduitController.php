<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Produit;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProduitController extends Controller
{
    /**
     * Récupère tous les produits avec pagination
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        // Valeurs par défaut et validation
        $limit = (int) $request->input('limit', 10);
        $page = (int) $request->input('page', 1);

        // Limiter les valeurs possibles pour éviter les problèmes de performance
        $allowedLimits = [10, 20, 50, 100];
        if (!in_array($limit, $allowedLimits)) {
            $limit = 10; // Valeur par défaut si la limite n'est pas valide
        }

        // Sélectionner uniquement les colonnes nécessaires pour améliorer les performances
        $produits = Produit::with([
            'categorie:id,nom,slug',
            'marchand:id,nomEntreprise',
            'reviews' => function($query) {
                $query->where('is_approved', true)->orderBy('created_at', 'desc')->limit(5);
            },
            'produitZonesLivraison' => function($query) {
                $query->where('actif', true);
            }
        ])
        ->select([
            'id', 'nom', 'slug', 'description', 'prix', 'stock',
            'images', 'categorie_id', 'marchand_id',
            'discount_price', 'discount_start_date', 'discount_end_date',
            'average_rating', 'reviews_count'
        ]);
        // Appliquer les filtres supplémentaires
        $this->applyFilters($produits, $request);
        // Paginer les résultats
        $produits = $produits->paginate($limit, ['*'], 'page', $page);

        // Ajouter les attributs calculés pour chaque produit
        foreach ($produits as $produit) {
            $produit->append(['average_rating', 'reviews_count']);

            // Ajouter le nombre de zones de livraison disponibles
            $produit->available_zones_count = $produit->produitZonesLivraison->count();
        }

        // Ajouter les limites autorisées à la réponse
        $response = $produits->toArray();
        $response['allowed_limits'] = $allowedLimits;

        return response()->json($response);
    }

    /**
     * Récupère un produit spécifique par son ID
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        $produit = Produit::with([
            'categorie',
            'marchand',
            'reviews' => function($query) {
                $query->where('is_approved', true)->orderBy('created_at', 'desc')->limit(5);
            },
            'variants' => function($query) {
                $query->select(['id', 'produit_id', 'sku', 'prix_supplement', 'stock', 'images', 'attributs']);
            },
            'produitZonesLivraison' => function($query) {
                $query->where('actif', true);
            }
        ])->findOrFail($id);

        // Assurez-vous que les attributs calculés sont chargés
        $produit->append(['average_rating', 'reviews_count']);

        // Ajouter les URLs des images et les attributs traités pour chaque variante
        if ($produit->variants) {
            foreach ($produit->variants as $variant) {
                $variant->append(['image_urls', 'processed_attributs']);

                // Remplacer les attributs bruts par les attributs traités
                $variant->attributs = $variant->processed_attributs;
            }
        }

        // Ajouter le nombre de zones de livraison disponibles
        $produit->available_zones_count = $produit->produitZonesLivraison->count();

        return response()->json($produit);
    }

    /**
     * Récupère un produit spécifique par son slug
     *
     * @param string $slug
     * @return JsonResponse
     */
    public function getBySlug(string $slug): JsonResponse
    {
        $produit = Produit::with([
            'categorie',
            'marchand',
            'reviews' => function($query) {
                $query->where('is_approved', true)->orderBy('created_at', 'desc')->limit(5);
            },
            'variants' => function($query) {
                $query->select(['id', 'produit_id', 'sku', 'prix_supplement', 'stock', 'images', 'attributs']);
            },
            'produitZonesLivraison' => function($query) {
                $query->where('actif', true);
            }
        ])->where('slug', $slug)->firstOrFail();

        // Assurez-vous que les attributs calculés sont chargés
        $produit->append(['average_rating', 'reviews_count']);

        // Ajouter les URLs des images et les attributs traités pour chaque variante
        if ($produit->variants) {
            foreach ($produit->variants as $variant) {
                $variant->append(['image_urls', 'processed_attributs']);

                // Remplacer les attributs bruts par les attributs traités
                $variant->attributs = $variant->processed_attributs;
            }
        }

        // Ajouter le nombre de zones de livraison disponibles
        $produit->available_zones_count = $produit->produitZonesLivraison->count();

        return response()->json($produit);
    }

    /**
     * Récupère les produits d'une catégorie spécifique avec pagination
     *
     * @param string $categorieId
     * @param Request $request
     * @return JsonResponse
     */
    public function getByCategorie(string $categorieId, Request $request): JsonResponse
    {
        // Valeurs par défaut et validation
        $limit = (int) $request->input('limit', 10);
        $page = (int) $request->input('page', 1);

        // Limiter les valeurs possibles pour éviter les problèmes de performance
        $allowedLimits = [10, 20, 50, 100];
        if (!in_array($limit, $allowedLimits)) {
            $limit = 10; // Valeur par défaut si la limite n'est pas valide
        }

        // Récupérer les IDs des sous-catégories pour inclure leurs produits
        $subcategoryIds = [];
        if ($request->input('include_subcategories', true)) {
            // Récupérer la catégorie principale
            $categorie = \App\Models\Categorie::find($categorieId);

            if ($categorie) {
                // Utiliser le nouveau champ category_path pour récupérer toutes les sous-catégories
                $subcategoryIds = \App\Models\Categorie::where('category_path', 'like', $categorie->category_path . '/%')
                    ->pluck('id')
                    ->toArray();
            } else {
                // Fallback à l'ancienne méthode si la catégorie n'existe pas
                $subcategoryIds = \App\Models\Categorie::where('categorie_parent_id', $categorieId)
                    ->pluck('id')
                    ->toArray();
            }
        }

        // Construire la requête
        $query = Produit::with([
            'categorie:id,nom,slug',
            'marchand:id,nomEntreprise',
            'reviews' => function($query) {
                $query->where('is_approved', true);
            },
            'produitZonesLivraison' => function($query) {
                $query->where('actif', true);
            }
        ])
        ->select([
            'id', 'nom', 'slug', 'description', 'prix', 'stock',
            'images', 'categorie_id', 'marchand_id',
            'discount_price', 'discount_start_date', 'discount_end_date'
        ]);
        // Filtrer par catégorie et sous-catégories
        if (!empty($subcategoryIds)) {
            $query->where(function($q) use ($categorieId, $subcategoryIds) {
                $q->where('categorie_id', $categorieId)
                  ->orWhereIn('categorie_id', $subcategoryIds);
            });
        } else {
            $query->where('categorie_id', $categorieId);
        }
        // Appliquer les filtres supplémentaires
        $this->applyFilters($query, $request);
        // Paginer les résultats
        $produits = $query->paginate($limit, ['*'], 'page', $page);

        // Ajouter les attributs calculés pour chaque produit
        foreach ($produits as $produit) {
            $produit->append(['average_rating', 'reviews_count']);

            // Ajouter le nombre de zones de livraison disponibles
            $produit->available_zones_count = $produit->produitZonesLivraison->count();
        }

        // Ajouter les limites autorisées à la réponse
        $response = $produits->toArray();
        $response['allowed_limits'] = $allowedLimits;

        return response()->json($response);
    }

    /**
     * Récupère les produits en vedette
     *
     * @param int $limit Nombre de produits à récupérer
     * @return JsonResponse
     */
    public function getFeatured(int $limit = 8): JsonResponse
    {
        $produits = Produit::with([
            'categorie',
            'marchand',
            'reviews' => function($query) {
                $query->where('is_approved', true);
            },
            'produitZonesLivraison' => function($query) {
                $query->where('actif', true);
            }
        ])
            ->inRandomOrder()
            ->limit($limit)
            ->get();

        // Assurez-vous que les attributs calculés sont chargés pour chaque produit
        foreach ($produits as $produit) {
            $produit->append(['average_rating', 'reviews_count']);

            // Ajouter le nombre de zones de livraison disponibles
            $produit->available_zones_count = $produit->produitZonesLivraison->count();
        }

        return response()->json($produits);
    }

    /**
     * Récupère les produits en promotion
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDiscounted(Request $request): JsonResponse
    {
        // Valeurs par défaut et validation
        $limit = (int) $request->input('limit', 10);
        $page = (int) $request->input('page', 1);

        // Limiter les valeurs possibles pour éviter les problèmes de performance
        $allowedLimits = [10, 20, 50, 100];
        if (!in_array($limit, $allowedLimits)) {
            $limit = 10; // Valeur par défaut si la limite n'est pas valide
        }

        $query = Produit::with([
            'categorie:id,nom,slug',
            'marchand:id,nomEntreprise',
            'reviews' => function($query) {
                $query->where('is_approved', true);
            },
            'produitZonesLivraison' => function($query) {
                $query->where('actif', true);
            }
        ])
        ->select([
            'id', 'nom', 'slug', 'description', 'prix', 'stock',
            'images', 'categorie_id', 'marchand_id',
            'discount_price', 'discount_start_date', 'discount_end_date'
        ])
        ->whereNotNull('discount_price')
        ->where('discount_price', '>', 0)
        ->where(function($q) {
            $now = now();
            $q->whereNull('discount_start_date')
              ->orWhere('discount_start_date', '<=', $now);
        })
        ->where(function($q) {
            $now = now();
            $q->whereNull('discount_end_date')
              ->orWhere('discount_end_date', '>=', $now);
        });

        // Appliquer les filtres supplémentaires
        $this->applyFilters($query, $request);

        // Paginer les résultats
        $produits = $query->paginate($limit, ['*'], 'page', $page);

        // Ajouter les attributs calculés pour chaque produit
        foreach ($produits as $produit) {
            $produit->append(['average_rating', 'reviews_count']);

            // Ajouter le nombre de zones de livraison disponibles
            $produit->available_zones_count = $produit->produitZonesLivraison->count();
        }

        // Ajouter les limites autorisées à la réponse
        $response = $produits->toArray();
        $response['allowed_limits'] = $allowedLimits;

        return response()->json($response);
    }

    /**
     * Recherche des produits
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        // Valeurs par défaut et validation
        $limit = (int) $request->input('limit', 10);
        $page = (int) $request->input('page', 1);
        $searchQuery = $request->input('q', '');

        // Limiter les valeurs possibles pour éviter les problèmes de performance
        $allowedLimits = [10, 20, 50, 100];
        if (!in_array($limit, $allowedLimits)) {
            $limit = 10; // Valeur par défaut si la limite n'est pas valide
        }

        $query = Produit::with([
            'categorie:id,nom,slug',
            'marchand:id,nomEntreprise',
            'reviews' => function($query) {
                $query->where('is_approved', true);
            },
            'produitZonesLivraison' => function($query) {
                $query->where('actif', true);
            }
        ])
        ->select([
            'id', 'nom', 'slug', 'description', 'prix', 'stock',
            'images', 'categorie_id', 'marchand_id',
            'discount_price', 'discount_start_date', 'discount_end_date'
        ])
        ->where(function($q) use ($searchQuery) {
            $q->where('nom', 'like', "%{$searchQuery}%")
              ->orWhere('description', 'like', "%{$searchQuery}%");
        });

        // Appliquer les filtres supplémentaires
        $this->applyFilters($query, $request);

        // Paginer les résultats
        $produits = $query->paginate($limit, ['*'], 'page', $page);

        // Ajouter les attributs calculés pour chaque produit
        foreach ($produits as $produit) {
            $produit->append(['average_rating', 'reviews_count']);

            // Ajouter le nombre de zones de livraison disponibles
            $produit->available_zones_count = $produit->produitZonesLivraison->count();
        }

        // Ajouter les limites autorisées à la réponse
        $response = $produits->toArray();
        $response['allowed_limits'] = $allowedLimits;

        return response()->json($response);
    }

    /**
     * Applique les filtres à la requête de produits
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param Request $request
     * @return void
     */
    protected function applyFilters($query, Request $request): void
    {
        // Filtrer par devise
        if ($request->has('currency')) {
            $currency = $request->input('currency');
            $query->where('currency', $currency);
        }

        // Filtrer par prix
        if ($request->has('min_price') && $request->has('max_price')) {
            $minPrice = (float) $request->input('min_price');
            $maxPrice = (float) $request->input('max_price');
            $query->whereBetween('prix', [$minPrice, $maxPrice]);
        } else if ($request->has('min_price')) {
            $minPrice = (float) $request->input('min_price');
            $query->where('prix', '>=', $minPrice);
        } else if ($request->has('max_price')) {
            $maxPrice = (float) $request->input('max_price');
            $query->where('prix', '<=', $maxPrice);
        }

        // Filtrer par disponibilité
        if ($request->has('in_stock') && $request->boolean('in_stock')) {
            $query->where('stock', '>', 0);
        }

        // Filtrer par notation minimale
        if ($request->has('min_rating')) {
            $minRating = (float) $request->input('min_rating');

            // Approche très simple: filtrer les produits qui ont au moins une review avec la note minimale
            // Cette approche n'est pas parfaite mais devrait fonctionner avec SQLite
            $query->whereHas('reviews', function($q) use ($minRating) {
                $q->where('is_approved', true)
                  ->where('rating', '>=', $minRating);
            });

            // Note: Cette approche filtre les produits qui ont au moins une review avec la note minimale,
            // ce qui n'est pas exactement la même chose que filtrer par note moyenne.
            // Mais c'est une solution de contournement qui devrait fonctionner avec SQLite.

            // Quand vous passerez à MySQL, vous pourrez utiliser cette approche plus précise:
            // $query->withAvg('reviews as avg_rating', 'rating')
            //       ->having('avg_rating', '>=', $minRating);
        }

        // Filtrer par promotion
        if ($request->has('on_sale') && $request->boolean('on_sale')) {
            $now = now();
            $query->whereNotNull('discount_price')
                ->where('discount_price', '>', 0)
                ->where(function($q) use ($now) {
                    $q->whereNull('discount_start_date')
                      ->orWhere('discount_start_date', '<=', $now);
                })
                ->where(function($q) use ($now) {
                    $q->whereNull('discount_end_date')
                      ->orWhere('discount_end_date', '>=', $now);
                });
        }

        // Filtrer par recherche
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('nom', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filtrer par sous-catégories
        if ($request->has('subcategories')) {
            $subcategories = explode(',', $request->input('subcategories'));
            if (!empty($subcategories)) {
                $query->whereIn('categorie_id', $subcategories);
            }
        }

        // Exclure un produit spécifique (utile pour les produits similaires)
        if ($request->has('exclude_product')) {
            $excludeProductId = $request->input('exclude_product');
            $query->where('id', '!=', $excludeProductId);
        }

        // Appliquer le tri
        $sortBy = $request->input('sort_by', 'default');
        switch ($sortBy) {
            case 'price-asc':
            case 'price_asc':
                $query->orderBy('prix', 'asc');
                break;
            case 'price-desc':
            case 'price_desc':
                $query->orderBy('prix', 'desc');
                break;
            case 'name-asc':
            case 'name_asc':
                $query->orderBy('nom', 'asc');
                break;
            case 'name-desc':
            case 'name_desc':
                $query->orderBy('nom', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'popularity':
                // Dans une application réelle, vous pourriez avoir une colonne pour la popularité
                // ou utiliser le nombre de ventes, de vues, etc.
                $query->orderBy('id', 'desc'); // Fallback pour l'exemple
                break;
            case 'rating-desc':
                // Trier par notation (du plus élevé au plus bas)
                // Approche simplifiée pour SQLite
                $query->leftJoin('reviews', function($join) {
                    $join->on('produits.id', '=', 'reviews.produit_id')
                         ->where('reviews.is_approved', '=', true);
                })
                ->select('produits.*')
                ->groupBy('produits.id')
                ->orderByRaw('COALESCE(MAX(reviews.rating), 0) DESC');

                // Note: Cette approche trie par la note maximale plutôt que par la moyenne,
                // mais c'est une solution de contournement qui devrait fonctionner avec SQLite.

                // Quand vous passerez à MySQL, vous pourrez utiliser cette approche plus précise:
                // $query->withAvg('reviews as avg_rating', 'rating')
                //       ->orderByDesc('avg_rating');
                break;
            default:
                // Tri par défaut (nouveautés)
                $query->orderBy('created_at', 'desc');
                break;
        }
    }

    /**
     * Transformer un produit pour l'API
     *
     * @param Produit $produit
     * @return array
     */
    private function transformProduit(Produit $produit): array
    {
        // Charger les relations nécessaires
        $produit->load(['categorie', 'marchand', 'attributs', 'variants', 'reviews', 'produitZonesLivraison']);

        // Calculer la note moyenne
        $averageRating = $produit->reviews->avg('note') ?? 0;
        $reviewsCount = $produit->reviews->count();

        // Compter le nombre de zones de livraison disponibles
        $availableZonesCount = $produit->produitZonesLivraison()
            ->where('actif', true)
            ->count();

        // Transformer les attributs
        $attributs = $produit->attributs->map(function ($attribut) {
            return [
                'id' => $attribut->id,
                'type' => $attribut->type,
                'nom' => $attribut->nom,
                'valeur' => $attribut->valeur,
                'code_couleur' => $attribut->code_couleur,
            ];
        });

        // Transformer les variantes
        $variants = $produit->variants->map(function ($variant) {
            // Charger les attributs de la variante
            $variant->load('attributs');

            return [
                'id' => $variant->id,
                'sku' => $variant->sku,
                'prix_supplement' => $variant->prix_supplement,
                'stock' => $variant->stock,
                'images' => $variant->images ? json_decode($variant->images) : [],
                'attributs' => $variant->attributs->map(function ($attribut) {
                    return [
                        'id' => $attribut->id,
                        'type' => $attribut->type,
                        'nom' => $attribut->nom,
                        'valeur' => $attribut->valeur,
                        'code_couleur' => $attribut->code_couleur,
                    ];
                }),
            ];
        });

        // Construire le tableau de réponse
        return [
            'id' => $produit->id,
            'nom' => $produit->nom,
            'slug' => $produit->slug,
            'description' => $produit->description,
            'prix' => $produit->prix,
            'currency' => $produit->currency,
            'stock' => $produit->stock,
            'images' => $produit->images ? json_decode($produit->images) : [],
            'categorie' => $produit->categorie ? [
                'id' => $produit->categorie->id,
                'nom' => $produit->categorie->nom,
                'slug' => $produit->categorie->slug,
            ] : null,
            'marchand' => $produit->marchand ? [
                'id' => $produit->marchand->id,
                'nom' => $produit->marchand->nomEntreprise,
            ] : null,
            'discount_price' => $produit->discount_price,
            'discount_start_date' => $produit->discount_start_date,
            'discount_end_date' => $produit->discount_end_date,
            'average_rating' => $averageRating,
            'reviews_count' => $reviewsCount,
            'attributs' => $attributs,
            'variants' => $variants,
            'available_zones_count' => $availableZonesCount,
        ];
    }
}
