<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ZoneLivraison;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ZoneLivraisonController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = ZoneLivraison::query();

        // Filtrer par type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filtrer par parent_id
        if ($request->has('parent_id')) {
            $query->where('parent_id', $request->parent_id);
        }

        // Filtrer par actif
        if ($request->has('actif')) {
            $query->where('actif', $request->actif);
        }

        // Recherche par nom
        if ($request->has('search')) {
            $query->where('nom', 'like', '%' . $request->search . '%');
        }

        // Pagination
        $perPage = $request->input('per_page', 15);
        $zones = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $zones,
            'message' => 'Zones de livraison récupérées avec succès'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'nom' => 'required|string|max:255',
            'type' => 'required|in:Pays,Region,Ville,Quartier',
            'parent_id' => 'nullable|exists:zones_livraison,id',
            'code' => 'nullable|string|max:50',
            'actif' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'message' => 'Validation échouée'
            ], 422);
        }

        $zone = ZoneLivraison::create($request->all());

        return response()->json([
            'success' => true,
            'data' => $zone,
            'message' => 'Zone de livraison créée avec succès'
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        $zone = ZoneLivraison::find($id);

        if (!$zone) {
            return response()->json([
                'success' => false,
                'message' => 'Zone de livraison non trouvée'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $zone,
            'message' => 'Zone de livraison récupérée avec succès'
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $zone = ZoneLivraison::find($id);

        if (!$zone) {
            return response()->json([
                'success' => false,
                'message' => 'Zone de livraison non trouvée'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'nom' => 'sometimes|required|string|max:255',
            'type' => 'sometimes|required|in:Pays,Region,Ville,Quartier',
            'parent_id' => 'nullable|exists:zones_livraison,id',
            'code' => 'nullable|string|max:50',
            'actif' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'message' => 'Validation échouée'
            ], 422);
        }

        $zone->update($request->all());

        return response()->json([
            'success' => true,
            'data' => $zone,
            'message' => 'Zone de livraison mise à jour avec succès'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function destroy(string $id): JsonResponse
    {
        $zone = ZoneLivraison::find($id);

        if (!$zone) {
            return response()->json([
                'success' => false,
                'message' => 'Zone de livraison non trouvée'
            ], 404);
        }

        // Vérifier si la zone a des enfants
        if ($zone->enfants()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de supprimer cette zone car elle contient des zones enfants'
            ], 400);
        }

        // Vérifier si la zone est utilisée par des marchands
        if ($zone->marchandZonesLivraison()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de supprimer cette zone car elle est utilisée par des marchands'
            ], 400);
        }

        // Vérifier si la zone est utilisée par des adresses
        if ($zone->adresses()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de supprimer cette zone car elle est utilisée par des adresses'
            ], 400);
        }

        $zone->delete();

        return response()->json([
            'success' => true,
            'message' => 'Zone de livraison supprimée avec succès'
        ]);
    }

    /**
     * Récupérer les enfants d'une zone de livraison.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getChildren(string $id): JsonResponse
    {
        $zone = ZoneLivraison::find($id);

        if (!$zone) {
            return response()->json([
                'success' => false,
                'message' => 'Zone de livraison non trouvée'
            ], 404);
        }

        $children = $zone->enfants()->get();

        return response()->json([
            'success' => true,
            'data' => $children,
            'message' => 'Enfants de la zone de livraison récupérés avec succès'
        ]);
    }

    /**
     * Récupérer l'arborescence complète des zones de livraison.
     *
     * @return JsonResponse
     */
    public function getTree(): JsonResponse
    {
        // Récupérer les pays (niveau racine)
        $countries = ZoneLivraison::where('type', 'Pays')
            ->where('actif', true)
            ->get();

        $tree = [];

        foreach ($countries as $country) {
            $tree[] = $this->buildTreeNode($country);
        }

        return response()->json([
            'success' => true,
            'data' => $tree,
            'message' => 'Arborescence des zones de livraison récupérée avec succès'
        ]);
    }

    /**
     * Construire un nœud de l'arborescence.
     *
     * @param ZoneLivraison $zone
     * @return array
     */
    private function buildTreeNode(ZoneLivraison $zone): array
    {
        $node = [
            'id' => $zone->id,
            'nom' => $zone->nom,
            'type' => $zone->type,
            'code' => $zone->code,
            'actif' => $zone->actif,
        ];

        $children = $zone->enfants()->where('actif', true)->get();

        if ($children->count() > 0) {
            $node['enfants'] = [];

            foreach ($children as $child) {
                $node['enfants'][] = $this->buildTreeNode($child);
            }
        }

        return $node;
    }
}
