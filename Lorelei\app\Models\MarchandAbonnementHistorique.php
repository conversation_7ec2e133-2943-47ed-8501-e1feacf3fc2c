<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MarchandAbonnementHistorique extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'marchand_abonnement_historique';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'marchand_id',
        'abonnement_id',
        'action',
        'type_abonnement_avant',
        'type_abonnement_apres',
        'statut_avant',
        'statut_apres',
        'prix_avant',
        'prix_apres',
        'montant_paye',
        'montant_rembourse',
        'date_debut_periode',
        'date_fin_periode',
        'date_action',
        'raison',
        'initie_par',
        'user_id',
        'reference_paiement',
        'methode_paiement',
        'code_promotion_utilise',
        'reduction_appliquee',
        'periode_essai_utilisee',
        'donnees_supplementaires',
        'commentaires_admin',
        'adresse_ip',
        'user_agent',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'prix_avant' => 'decimal:2',
        'prix_apres' => 'decimal:2',
        'montant_paye' => 'decimal:2',
        'montant_rembourse' => 'decimal:2',
        'reduction_appliquee' => 'decimal:2',
        'date_debut_periode' => 'datetime',
        'date_fin_periode' => 'datetime',
        'date_action' => 'datetime',
        'periode_essai_utilisee' => 'boolean',
        'donnees_supplementaires' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Marchand concerné par l'historique
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Abonnement concerné (peut être null si supprimé)
     */
    public function abonnement(): BelongsTo
    {
        return $this->belongsTo(MarchandAbonnement::class, 'abonnement_id');
    }

    /**
     * Utilisateur qui a initié l'action
     */
    public function utilisateur(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Crée une entrée d'historique pour une action donnée
     */
    public static function enregistrerAction(
        int $marchandId,
        string $action,
        array $donnees
    ): self {
        return self::create(array_merge([
            'marchand_id' => $marchandId,
            'action' => $action,
            'date_action' => now(),
            'initie_par' => 'systeme',
            'adresse_ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ], $donnees));
    }

    /**
     * Enregistre un changement d'abonnement
     */
    public static function enregistrerChangementAbonnement(
        MarchandAbonnement $ancienAbonnement,
        MarchandAbonnement $nouvelAbonnement,
        ?string $raison = null,
        string $initiePar = 'marchand',
        ?int $userId = null
    ): self {
        return self::enregistrerAction($nouvelAbonnement->marchand_id, 'upgrade', [
            'abonnement_id' => $nouvelAbonnement->id,
            'type_abonnement_avant' => $ancienAbonnement->type_abonnement,
            'type_abonnement_apres' => $nouvelAbonnement->type_abonnement,
            'statut_avant' => $ancienAbonnement->statut,
            'statut_apres' => $nouvelAbonnement->statut,
            'prix_avant' => $ancienAbonnement->prix_mensuel,
            'prix_apres' => $nouvelAbonnement->prix_mensuel,
            'date_debut_periode' => $nouvelAbonnement->date_debut,
            'date_fin_periode' => $nouvelAbonnement->date_fin,
            'raison' => $raison,
            'initie_par' => $initiePar,
            'user_id' => $userId,
        ]);
    }

    /**
     * Enregistre un paiement d'abonnement
     */
    public static function enregistrerPaiement(
        int $marchandId,
        int $abonnementId,
        float $montant,
        string $referencePaiement,
        string $methodePaiement,
        ?string $codePromotion = null,
        ?float $reductionAppliquee = null
    ): self {
        return self::enregistrerAction($marchandId, 'renouvellement', [
            'abonnement_id' => $abonnementId,
            'montant_paye' => $montant,
            'reference_paiement' => $referencePaiement,
            'methode_paiement' => $methodePaiement,
            'code_promotion_utilise' => $codePromotion,
            'reduction_appliquee' => $reductionAppliquee,
            'initie_par' => 'marchand',
        ]);
    }

    /**
     * Enregistre une suspension d'abonnement
     */
    public static function enregistrerSuspension(
        int $marchandId,
        int $abonnementId,
        string $raison,
        string $initiePar = 'systeme',
        ?int $userId = null
    ): self {
        return self::enregistrerAction($marchandId, 'suspension', [
            'abonnement_id' => $abonnementId,
            'raison' => $raison,
            'initie_par' => $initiePar,
            'user_id' => $userId,
        ]);
    }

    /**
     * Enregistre une annulation d'abonnement
     */
    public static function enregistrerAnnulation(
        int $marchandId,
        int $abonnementId,
        string $raison,
        ?float $montantRembourse = null,
        string $initiePar = 'marchand',
        ?int $userId = null
    ): self {
        return self::enregistrerAction($marchandId, 'annulation', [
            'abonnement_id' => $abonnementId,
            'raison' => $raison,
            'montant_rembourse' => $montantRembourse,
            'initie_par' => $initiePar,
            'user_id' => $userId,
        ]);
    }

    /**
     * Obtient un résumé des actions pour un marchand
     */
    public static function getResumeMarchand(int $marchandId): array
    {
        $historique = self::where('marchand_id', $marchandId)
            ->orderBy('date_action', 'desc')
            ->get();

        return [
            'total_actions' => $historique->count(),
            'derniere_action' => $historique->first(),
            'total_paye' => $historique->sum('montant_paye'),
            'total_rembourse' => $historique->sum('montant_rembourse'),
            'actions_par_type' => $historique->groupBy('action')->map->count(),
            'changements_abonnement' => $historique->whereIn('action', ['upgrade', 'downgrade'])->count(),
        ];
    }

    /**
     * Scope pour filtrer par période
     */
    public function scopePeriode($query, $dateDebut, $dateFin)
    {
        return $query->whereBetween('date_action', [$dateDebut, $dateFin]);
    }

    /**
     * Scope pour filtrer par action
     */
    public function scopeAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope pour filtrer par type d'abonnement
     */
    public function scopeTypeAbonnement($query, $type)
    {
        return $query->where('type_abonnement_apres', $type);
    }
}
