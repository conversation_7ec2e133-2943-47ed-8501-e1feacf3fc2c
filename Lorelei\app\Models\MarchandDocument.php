<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class MarchandDocument extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'marchand_documents';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'marchand_id',
        'type_document',
        'nom_original',
        'nom_stockage',
        'chemin_fichier',
        'extension',
        'taille_fichier',
        'mime_type',
        'hash_fichier',
        'statut_validation',
        'validateur_id',
        'date_validation',
        'commentaires_validation',
        'raison_rejet',
        'date_upload',
        'date_expiration',
        'date_derniere_modification',
        'est_obligatoire',
        'est_confidentiel',
        'version',
        'metadonnees',
        'est_crypte',
        'cle_cryptage',
        'adresse_ip_upload',
        'user_agent_upload',
        'ocr_effectue',
        'donnees_ocr',
        'score_qualite',
        'verifications_automatiques',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'date_upload' => 'datetime',
        'date_validation' => 'datetime',
        'date_expiration' => 'datetime',
        'date_derniere_modification' => 'datetime',
        'est_obligatoire' => 'boolean',
        'est_confidentiel' => 'boolean',
        'est_crypte' => 'boolean',
        'ocr_effectue' => 'boolean',
        'score_qualite' => 'decimal:2',
        'metadonnees' => 'array',
        'donnees_ocr' => 'array',
        'verifications_automatiques' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Marchand propriétaire du document
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Validateur du document
     */
    public function validateur(): BelongsTo
    {
        return $this->belongsTo(User::class, 'validateur_id');
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Vérifie si le document est validé
     */
    public function estValide(): bool
    {
        return $this->statut_validation === 'valide';
    }

    /**
     * Vérifie si le document est expiré
     */
    public function estExpire(): bool
    {
        return $this->date_expiration && $this->date_expiration->isPast();
    }

    /**
     * Vérifie si le document va expirer bientôt (dans les 30 jours)
     */
    public function vaExpirer(): bool
    {
        return $this->date_expiration &&
               $this->date_expiration->isFuture() &&
               $this->date_expiration->diffInDays(now()) <= 30;
    }

    /**
     * Obtient l'URL du document (sécurisée)
     */
    public function getUrlSecurisee(): string
    {
        // Retourner une URL temporaire sécurisée
        return Storage::temporaryUrl($this->chemin_fichier, now()->addMinutes(30));
    }

    /**
     * Obtient la taille du fichier formatée
     */
    public function getTailleFormatee(): string
    {
        $bytes = $this->taille_fichier;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Valide le document
     */
    public function valider(int $validateurId, ?string $commentaires = null): bool
    {
        $this->update([
            'statut_validation' => 'valide',
            'validateur_id' => $validateurId,
            'date_validation' => now(),
            'commentaires_validation' => $commentaires,
        ]);

        return true;
    }

    /**
     * Rejette le document
     */
    public function rejeter(int $validateurId, string $raison, ?string $commentaires = null): bool
    {
        $this->update([
            'statut_validation' => 'rejete',
            'validateur_id' => $validateurId,
            'date_validation' => now(),
            'raison_rejet' => $raison,
            'commentaires_validation' => $commentaires,
        ]);

        return true;
    }

    /**
     * Obtient les types de documents requis selon le type de business
     */
    public static function getTypesRequis(string $typeBusiness): array
    {
        $typesBase = [
            'cni_recto' => 'CNI (Recto)',
            'cni_verso' => 'CNI (Verso)',
            'photo_avec_cni' => 'Photo avec CNI en main',
        ];

        $typesSpecifiques = [
            'individuel' => [
                'justificatif_domicile' => 'Justificatif de domicile',
            ],
            'entreprise' => [
                'registre_commerce' => 'Registre de commerce',
                'statuts_entreprise' => 'Statuts de l\'entreprise',
                'rib_bancaire' => 'RIB/IBAN bancaire',
                'justificatif_domicile' => 'Justificatif de domicile',
            ],
            'cooperative' => [
                'recepisse_declaration' => 'Récépissé de déclaration',
                'statuts_entreprise' => 'Statuts de l\'association',
                'rib_bancaire' => 'RIB/IBAN bancaire',
            ],
            'grande_entreprise' => [
                'registre_commerce' => 'Registre de commerce',
                'statuts_entreprise' => 'Statuts de l\'entreprise',
                'bilan_comptable' => 'Bilan comptable',
                'rib_bancaire' => 'RIB/IBAN bancaire',
                'declaration_fiscale' => 'Déclaration fiscale',
            ],
        ];

        return array_merge($typesBase, $typesSpecifiques[$typeBusiness] ?? []);
    }

    /**
     * Obtient le libellé d'un type de document
     */
    public static function getLibelleType(string $type): string
    {
        $libelles = [
            'cni_recto' => 'CNI (Recto)',
            'cni_verso' => 'CNI (Verso)',
            'passeport' => 'Passeport',
            'permis_conduire' => 'Permis de conduire',
            'photo_avec_cni' => 'Photo avec CNI en main',
            'registre_commerce' => 'Registre de commerce',
            'statuts_entreprise' => 'Statuts de l\'entreprise',
            'kbis' => 'Extrait Kbis',
            'recepisse_declaration' => 'Récépissé de déclaration',
            'autorisation_exercice' => 'Autorisation d\'exercer',
            'rib_bancaire' => 'RIB/IBAN bancaire',
            'attestation_bancaire' => 'Attestation bancaire',
            'justificatif_mobile_money' => 'Justificatif Mobile Money',
            'bilan_comptable' => 'Bilan comptable',
            'declaration_fiscale' => 'Déclaration fiscale',
            'justificatif_domicile' => 'Justificatif de domicile',
            'bail_commercial' => 'Bail commercial',
            'facture_electricite' => 'Facture d\'électricité',
            'facture_eau' => 'Facture d\'eau',
            'photo_profil' => 'Photo de profil',
            'logo_entreprise' => 'Logo de l\'entreprise',
            'certificat_formation' => 'Certificat de formation',
            'autre' => 'Autre document',
        ];

        return $libelles[$type] ?? 'Document inconnu';
    }

    /**
     * Scope pour filtrer par statut
     */
    public function scopeStatut($query, $statut)
    {
        return $query->where('statut_validation', $statut);
    }

    /**
     * Scope pour filtrer par type
     */
    public function scopeType($query, $type)
    {
        return $query->where('type_document', $type);
    }

    /**
     * Scope pour les documents obligatoires
     */
    public function scopeObligatoires($query)
    {
        return $query->where('est_obligatoire', true);
    }

    /**
     * Scope pour les documents expirés ou qui vont expirer
     */
    public function scopeExpiresOuAExpirer($query, $jours = 30)
    {
        return $query->where(function ($q) use ($jours) {
            $q->where('date_expiration', '<', now())
              ->orWhere('date_expiration', '<', now()->addDays($jours));
        });
    }
}
