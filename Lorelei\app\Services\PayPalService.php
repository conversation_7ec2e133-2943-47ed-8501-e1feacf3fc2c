<?php

namespace App\Services;

use PayPal\Auth\OAuthTokenCredential;
use PayPal\Rest\ApiContext;
use PayPal\Api\Payment;
use PayPal\Api\PaymentExecution;
use PayPal\Api\Transaction;
use PayPal\Api\Amount;
use PayPal\Api\Details;
use PayPal\Api\Item;
use PayPal\Api\ItemList;
use PayPal\Api\Payer;
use PayPal\Api\RedirectUrls;
use PayPal\Api\ExecutePayment;
use PayPal\Exception\PayPalException;
use Illuminate\Support\Facades\Log;

class PayPalService
{
    private $apiContext;
    private $clientId;
    private $clientSecret;
    private $mode;

    public function __construct()
    {
        $this->clientId = config('services.paypal.client_id');
        $this->clientSecret = config('services.paypal.client_secret');
        $this->mode = config('services.paypal.mode');

        $this->apiContext = new ApiContext(
            new OAuthTokenCredential($this->clientId, $this->clientSecret)
        );

        $this->apiContext->setConfig([
            'mode' => $this->mode,
            'log.LogEnabled' => true,
            'log.FileName' => storage_path('logs/paypal.log'),
            'log.LogLevel' => 'ERROR',
            'validation.level' => 'log',
            'cache.enabled' => true,
        ]);
    }

    /**
     * Créer un paiement PayPal
     *
     * @param array $orderData
     * @return array
     */
    public function createPayment(array $orderData): array
    {
        try {
            // Configuration du payeur
            $payer = new Payer();
            $payer->setPaymentMethod('paypal');

            // Configuration des articles
            $items = [];
            $subtotal = 0;

            foreach ($orderData['items'] as $item) {
                $paypalItem = new Item();
                $paypalItem->setName($item['name'])
                    ->setCurrency('XOF') // Franc CFA
                    ->setQuantity($item['quantity'])
                    ->setPrice($item['price']);

                $items[] = $paypalItem;
                $subtotal += $item['price'] * $item['quantity'];
            }

            $itemList = new ItemList();
            $itemList->setItems($items);

            // Configuration des détails du montant
            $details = new Details();
            $details->setSubtotal($subtotal)
                ->setTax($orderData['tax'] ?? 0)
                ->setShipping($orderData['shipping'] ?? 0);

            // Configuration du montant total
            $amount = new Amount();
            $amount->setCurrency('XOF')
                ->setTotal($orderData['total'])
                ->setDetails($details);

            // Configuration de la transaction
            $transaction = new Transaction();
            $transaction->setAmount($amount)
                ->setItemList($itemList)
                ->setDescription($orderData['description'] ?? 'Commande Loʁelei Marketplace')
                ->setInvoiceNumber($orderData['order_id']);

            // URLs de redirection
            $redirectUrls = new RedirectUrls();
            $redirectUrls->setReturnUrl($orderData['return_url'])
                ->setCancelUrl($orderData['cancel_url']);

            // Création du paiement
            $payment = new Payment();
            $payment->setIntent('sale')
                ->setPayer($payer)
                ->setRedirectUrls($redirectUrls)
                ->setTransactions([$transaction]);

            $payment->create($this->apiContext);

            return [
                'success' => true,
                'payment_id' => $payment->getId(),
                'approval_url' => $this->getApprovalUrl($payment),
                'payment' => $payment
            ];

        } catch (PayPalException $e) {
            Log::error('PayPal Payment Creation Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Exécuter un paiement PayPal
     *
     * @param string $paymentId
     * @param string $payerId
     * @return array
     */
    public function executePayment(string $paymentId, string $payerId): array
    {
        try {
            $payment = Payment::get($paymentId, $this->apiContext);

            $execution = new PaymentExecution();
            $execution->setPayerId($payerId);

            $result = $payment->execute($execution, $this->apiContext);

            return [
                'success' => true,
                'payment' => $result,
                'transaction_id' => $result->getTransactions()[0]->getRelatedResources()[0]->getSale()->getId(),
                'state' => $result->getState()
            ];

        } catch (PayPalException $e) {
            Log::error('PayPal Payment Execution Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Obtenir les détails d'un paiement
     *
     * @param string $paymentId
     * @return array
     */
    public function getPaymentDetails(string $paymentId): array
    {
        try {
            $payment = Payment::get($paymentId, $this->apiContext);

            return [
                'success' => true,
                'payment' => $payment,
                'state' => $payment->getState(),
                'total' => $payment->getTransactions()[0]->getAmount()->getTotal(),
                'currency' => $payment->getTransactions()[0]->getAmount()->getCurrency()
            ];

        } catch (PayPalException $e) {
            Log::error('PayPal Get Payment Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Rembourser un paiement
     *
     * @param string $saleId
     * @param float|null $amount
     * @return array
     */
    public function refundPayment(string $saleId, ?float $amount = null): array
    {
        try {
            $sale = \PayPal\Api\Sale::get($saleId, $this->apiContext);

            $refund = new \PayPal\Api\Refund();
            if ($amount) {
                $refundAmount = new Amount();
                $refundAmount->setCurrency('XOF')->setTotal($amount);
                $refund->setAmount($refundAmount);
            }

            $refundedSale = $sale->refund($refund, $this->apiContext);

            return [
                'success' => true,
                'refund' => $refundedSale,
                'refund_id' => $refundedSale->getId(),
                'state' => $refundedSale->getState()
            ];

        } catch (PayPalException $e) {
            Log::error('PayPal Refund Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Vérifier un webhook PayPal
     *
     * @param array $headers
     * @param string $body
     * @return bool
     */
    public function verifyWebhook(array $headers, string $body): bool
    {
        try {
            $webhookId = config('services.paypal.webhook_id');
            
            if (!$webhookId) {
                Log::warning('PayPal Webhook ID not configured');
                return false;
            }

            // Ici vous pouvez implémenter la vérification du webhook
            // selon la documentation PayPal
            
            return true;

        } catch (\Exception $e) {
            Log::error('PayPal Webhook Verification Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtenir l'URL d'approbation du paiement
     *
     * @param Payment $payment
     * @return string|null
     */
    private function getApprovalUrl(Payment $payment): ?string
    {
        $links = $payment->getLinks();
        
        foreach ($links as $link) {
            if ($link->getRel() === 'approval_url') {
                return $link->getHref();
            }
        }
        
        return null;
    }

    /**
     * Convertir un montant en centimes pour PayPal
     *
     * @param float $amount
     * @return string
     */
    public function formatAmount(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * Vérifier si PayPal est configuré
     *
     * @return bool
     */
    public function isConfigured(): bool
    {
        return !empty($this->clientId) && !empty($this->clientSecret);
    }
}
