<?php

if (!function_exists('cdn_url')) {
    /**
     * Génère une URL CDN pour un fichier
     *
     * @param string $path Chemin du fichier
     * @return string URL complète
     */
    function cdn_url(string $path): string
    {
        return app(App\Services\CdnService::class)->url($path);
    }
}

if (!function_exists('thumbnail_url')) {
    /**
     * Génère l'URL d'une miniature
     *
     * @param string $imagePath Chemin de l'image originale
     * @param string $size Taille de la miniature (small, medium, large)
     * @return string URL de la miniature
     */
    function thumbnail_url(string $imagePath, string $size = 'medium'): string
    {
        return App\Helpers\ThumbnailHelper::getThumbnailUrl($imagePath, $size);
    }
}

if (!function_exists('product_thumbnail')) {
    /**
     * Génère l'URL d'une miniature de produit
     *
     * @param string $imagePath Chemin de l'image du produit
     * @param string $size Taille de la miniature (small, medium, large)
     * @return string URL de la miniature
     */
    function product_thumbnail(string $imagePath, string $size = 'medium'): string
    {
        return App\Helpers\ThumbnailHelper::getProductThumbnail($imagePath, $size);
    }
}

if (!function_exists('category_thumbnail')) {
    /**
     * Génère l'URL d'une miniature de catégorie
     *
     * @param string $imagePath Chemin de l'image de la catégorie
     * @param string $size Taille de la miniature (small, medium, large)
     * @return string URL de la miniature
     */
    function category_thumbnail(string $imagePath, string $size = 'medium'): string
    {
        return App\Helpers\ThumbnailHelper::getCategoryThumbnail($imagePath, $size);
    }
}

if (!function_exists('banner_thumbnail')) {
    /**
     * Génère l'URL d'une miniature de bannière
     *
     * @param string $imagePath Chemin de l'image de la bannière
     * @param string $size Taille de la miniature (small, medium, large)
     * @return string URL de la miniature
     */
    function banner_thumbnail(string $imagePath, string $size = 'medium'): string
    {
        return App\Helpers\ThumbnailHelper::getBannerThumbnail($imagePath, $size);
    }
}

if (!function_exists('review_thumbnail')) {
    /**
     * Génère l'URL d'une miniature de review
     *
     * @param string $imagePath Chemin de l'image de la review
     * @param string $size Taille de la miniature (small, medium, large)
     * @return string URL de la miniature
     */
    function review_thumbnail(string $imagePath, string $size = 'medium'): string
    {
        return App\Helpers\ThumbnailHelper::getReviewThumbnail($imagePath, $size);
    }
}
