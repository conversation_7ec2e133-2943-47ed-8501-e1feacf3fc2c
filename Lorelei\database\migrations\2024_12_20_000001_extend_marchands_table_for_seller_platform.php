<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('marchands', function (Blueprint $table) {
            // Informations de localisation business
            $table->string('pays_business', 100)->nullable()->after('nomEntreprise');
            $table->string('ville_business', 100)->nullable()->after('pays_business');

            // Type de business
            $table->enum('type_business', [
                'individuel',
                'entreprise',
                'cooperative',
                'grande_entreprise'
            ])->nullable()->after('ville_business');

            // Statut de validation
            $table->enum('statut_validation', [
                'en_attente',           // Inscription en cours
                'documents_requis',     // Documents manquants
                'en_verification',      // Documents soumis, en cours de vérification
                'valide',              // Marchand validé et actif
                'rejete',              // Candidature rejetée
                'suspendu'             // Compte suspendu
            ])->default('en_attente')->after('type_business');

            // Étape actuelle du processus d'inscription
            $table->enum('etape_inscription', [
                'bienvenue',           // Page de bienvenue
                'business_info',       // Informations business
                'documents',           // Upload documents
                'verification',        // En cours de vérification
                'termine'             // Inscription terminée
            ])->default('bienvenue')->after('statut_validation');

            // Documents et vérification
            $table->json('documents_soumis')->nullable()->after('etape_inscription');
            $table->json('documents_requis')->nullable()->after('documents_soumis');
            $table->timestamp('date_soumission_documents')->nullable()->after('documents_requis');
            $table->timestamp('date_validation')->nullable()->after('date_soumission_documents');
            $table->text('commentaires_validation')->nullable()->after('date_validation');
            $table->unsignedBigInteger('validateur_id')->nullable()->after('commentaires_validation');

            // Informations de contact
            $table->string('telephone_principal', 20)->nullable()->after('validateur_id');
            $table->string('telephone_secondaire', 20)->nullable()->after('telephone_principal');
            $table->string('email_business')->nullable()->after('telephone_secondaire');
            $table->string('site_web')->nullable()->after('email_business');

            // Informations de paiement
            $table->enum('methode_paiement_preferee', [
                'bancaire',
                'orange_money',
                'mtn_money',
                'mixte'
            ])->nullable()->after('site_web');

            // Informations bancaires (cryptées)
            $table->text('iban_crypte')->nullable()->after('methode_paiement_preferee');
            $table->string('nom_titulaire_compte')->nullable()->after('iban_crypte');

            // Mobile Money
            $table->string('numero_orange_money')->nullable()->after('nom_titulaire_compte');
            $table->string('numero_mtn_money')->nullable()->after('numero_orange_money');

            // Informations business supplémentaires
            $table->text('description_business')->nullable()->after('numero_mtn_money');
            $table->json('categories_produits')->nullable()->after('description_business'); // Catégories d'intérêt
            $table->decimal('chiffre_affaires_estime', 15, 2)->nullable()->after('categories_produits');
            $table->integer('nombre_employes')->nullable()->after('chiffre_affaires_estime');

            // Préférences et paramètres
            $table->boolean('accepte_conditions')->default(false)->after('nombre_employes');
            $table->boolean('accepte_newsletter')->default(true)->after('accepte_conditions');
            $table->string('langue_preferee', 5)->default('fr')->after('accepte_newsletter');
            $table->json('notifications_preferences')->nullable()->after('langue_preferee');

            // Métadonnées
            $table->string('source_inscription')->nullable()->after('notifications_preferences'); // Comment il a connu la plateforme
            $table->string('code_parrainage')->nullable()->after('source_inscription');
            $table->unsignedBigInteger('parrain_id')->nullable()->after('code_parrainage');

            // Index pour les recherches fréquentes avec noms personnalisés
            $table->index(['statut_validation', 'etape_inscription'], 'm_statut_etape_idx');
            $table->index(['pays_business', 'ville_business'], 'm_pays_ville_idx');
            $table->index(['type_business'], 'm_type_business_idx');
            $table->index(['date_validation'], 'm_date_validation_idx');

            // Contraintes de clé étrangère
            $table->foreign('validateur_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('parrain_id')->references('id')->on('marchands')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('marchands', function (Blueprint $table) {
            // Supprimer les contraintes de clé étrangère
            $table->dropForeign(['validateur_id']);
            $table->dropForeign(['parrain_id']);

            // Supprimer les index avec noms personnalisés
            $table->dropIndex('m_statut_etape_idx');
            $table->dropIndex('m_pays_ville_idx');
            $table->dropIndex('m_type_business_idx');
            $table->dropIndex('m_date_validation_idx');

            // Supprimer toutes les nouvelles colonnes
            $table->dropColumn([
                'pays_business',
                'ville_business',
                'type_business',
                'statut_validation',
                'etape_inscription',
                'documents_soumis',
                'documents_requis',
                'date_soumission_documents',
                'date_validation',
                'commentaires_validation',
                'validateur_id',
                'telephone_principal',
                'telephone_secondaire',
                'email_business',
                'site_web',
                'methode_paiement_preferee',
                'iban_crypte',
                'nom_titulaire_compte',
                'numero_orange_money',
                'numero_mtn_money',
                'description_business',
                'categories_produits',
                'chiffre_affaires_estime',
                'nombre_employes',
                'accepte_conditions',
                'accepte_newsletter',
                'langue_preferee',
                'notifications_preferences',
                'source_inscription',
                'code_parrainage',
                'parrain_id'
            ]);
        });
    }
};
