<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marchand_abonnements', function (Blueprint $table) {
            $table->id();

            // Relation avec le marchand
            $table->foreignId('marchand_id')->constrained('marchands')->onDelete('cascade');

            // Type d'abonnement
            $table->enum('type_abonnement', [
                'gratuit',      // Modèle de base (sans abonnement)
                'basique',      // 32,797.85 FCFA/mois
                'premium',      // 65,595.70 FCFA/mois
                'elite'         // 131,191.40 FCFA/mois
            ])->default('gratuit');

            // Statut de l'abonnement
            $table->enum('statut', [
                'actif',        // Abonnement en cours
                'expire',       // Abonnement expiré
                'suspendu',     // Abonnement suspendu (non-paiement)
                'annule',       // Abonnement annulé par le marchand
                'en_attente'    // En attente de paiement
            ])->default('actif');

            // Dates importantes
            $table->timestamp('date_debut')->useCurrent();
            $table->timestamp('date_fin')->nullable();
            $table->timestamp('date_prochaine_facturation')->nullable();
            $table->timestamp('date_expiration_grace')->nullable(); // Période de grâce après expiration

            // Informations financières
            $table->decimal('prix_mensuel', 10, 2)->default(0); // Prix payé pour cet abonnement
            $table->decimal('commission_taux_min', 5, 2)->nullable(); // Taux de commission minimum (ex: 2.00 pour 2%)
            $table->decimal('commission_taux_max', 5, 2)->nullable(); // Taux de commission maximum (ex: 4.00 pour 4%)
            $table->decimal('reduction_logistique', 5, 2)->default(0); // Pourcentage de réduction logistique

            // Limites et quotas
            $table->integer('limite_produits')->nullable(); // Nombre max de produits (null = illimité)
            $table->integer('limite_commandes_mois')->nullable(); // Nombre max de commandes par mois
            $table->integer('limite_campagnes_mois')->default(0); // Nombre de campagnes sponsorisées par mois
            $table->boolean('acces_analytics_avancees')->default(false);
            $table->boolean('acces_support_prioritaire')->default(false);
            $table->boolean('acces_gestionnaire_dedie')->default(false);
            $table->boolean('acces_ia_predictive')->default(false);
            $table->boolean('acces_evenements_exclusifs')->default(false);

            // Historique des changements
            $table->enum('type_abonnement_precedent', [
                'gratuit', 'basique', 'premium', 'elite'
            ])->nullable();
            $table->timestamp('date_changement_abonnement')->nullable();
            $table->text('raison_changement')->nullable();

            // Facturation
            $table->enum('mode_facturation', [
                'mensuel',
                'annuel'
            ])->default('mensuel');
            $table->boolean('facturation_automatique')->default(true);
            $table->string('methode_paiement_abonnement')->nullable(); // 'carte', 'virement', 'mobile_money'

            // Essais et promotions
            $table->boolean('est_periode_essai')->default(false);
            $table->timestamp('fin_periode_essai')->nullable();
            $table->string('code_promotion')->nullable();
            $table->decimal('reduction_promotion', 5, 2)->nullable(); // Pourcentage de réduction

            // Notifications et rappels
            $table->timestamp('derniere_notification_expiration')->nullable();
            $table->integer('nombre_rappels_paiement')->default(0);
            $table->timestamp('dernier_rappel_paiement')->nullable();

            // Métadonnées
            $table->json('fonctionnalites_activees')->nullable(); // Liste des fonctionnalités spécifiques activées
            $table->json('limites_personnalisees')->nullable(); // Limites spécifiques pour ce marchand
            $table->text('notes_admin')->nullable(); // Notes internes pour l'administration

            $table->timestamps();

            // Index pour optimiser les requêtes avec noms personnalisés
            $table->index(['marchand_id', 'statut'], 'ma_marchand_statut_idx');
            $table->index(['type_abonnement', 'statut'], 'ma_type_statut_idx');
            $table->index(['date_fin', 'statut'], 'ma_date_fin_statut_idx');
            $table->index(['date_prochaine_facturation'], 'ma_prochaine_fact_idx');
            $table->index(['est_periode_essai', 'fin_periode_essai'], 'ma_essai_fin_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marchand_abonnements');
    }
};
