<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marchand_abonnement_historique', function (Blueprint $table) {
            $table->id();

            // Relations
            $table->foreignId('marchand_id')->constrained('marchands')->onDelete('cascade');
            $table->foreignId('abonnement_id')->nullable()->constrained('marchand_abonnements')->onDelete('set null');

            // Informations de l'événement
            $table->enum('action', [
                'creation',         // Création d'un nouvel abonnement
                'upgrade',          // Passage à un niveau supérieur
                'downgrade',        // Passage à un niveau inférieur
                'renouvellement',   // Renouvellement automatique
                'suspension',       // Suspension pour non-paiement
                'reactivation',     // Réactivation après suspension
                'annulation',       // Annulation par le marchand
                'expiration',       // Expiration naturelle
                'modification'      // Modification des paramètres
            ]);

            // Détails de l'abonnement au moment de l'action
            $table->enum('type_abonnement_avant', [
                'gratuit', 'basique', 'premium', 'elite'
            ])->nullable();

            $table->enum('type_abonnement_apres', [
                'gratuit', 'basique', 'premium', 'elite'
            ]);

            $table->enum('statut_avant', [
                'actif', 'expire', 'suspendu', 'annule', 'en_attente'
            ])->nullable();

            $table->enum('statut_apres', [
                'actif', 'expire', 'suspendu', 'annule', 'en_attente'
            ]);

            // Informations financières
            $table->decimal('prix_avant', 10, 2)->nullable();
            $table->decimal('prix_apres', 10, 2);
            $table->decimal('montant_paye', 10, 2)->nullable(); // Montant effectivement payé
            $table->decimal('montant_rembourse', 10, 2)->nullable(); // Montant remboursé si applicable

            // Dates
            $table->timestamp('date_debut_periode')->nullable();
            $table->timestamp('date_fin_periode')->nullable();
            $table->timestamp('date_action')->useCurrent();

            // Détails de l'action
            $table->text('raison')->nullable(); // Raison du changement
            $table->enum('initie_par', [
                'marchand',     // Action initiée par le marchand
                'admin',        // Action initiée par un administrateur
                'systeme'       // Action automatique du système
            ])->default('marchand');

            $table->unsignedBigInteger('user_id')->nullable(); // ID de l'utilisateur qui a initié l'action
            $table->string('reference_paiement')->nullable(); // Référence de transaction si applicable
            $table->string('methode_paiement')->nullable(); // Méthode de paiement utilisée

            // Promotion et réductions
            $table->string('code_promotion_utilise')->nullable();
            $table->decimal('reduction_appliquee', 5, 2)->nullable();
            $table->boolean('periode_essai_utilisee')->default(false);

            // Métadonnées
            $table->json('donnees_supplementaires')->nullable(); // Données JSON pour informations additionnelles
            $table->text('commentaires_admin')->nullable(); // Commentaires internes
            $table->string('adresse_ip')->nullable(); // IP de l'utilisateur lors de l'action
            $table->text('user_agent')->nullable(); // User agent du navigateur

            $table->timestamps();

            // Index pour optimiser les requêtes avec noms personnalisés
            $table->index(['marchand_id', 'date_action'], 'mah_marchand_date_idx');
            $table->index(['action', 'date_action'], 'mah_action_date_idx');
            $table->index(['type_abonnement_apres', 'date_action'], 'mah_type_abo_date_idx');
            $table->index(['initie_par', 'date_action'], 'mah_initie_date_idx');
            $table->index(['reference_paiement'], 'mah_ref_paiement_idx');

            // Contrainte de clé étrangère
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marchand_abonnement_historique');
    }
};
