<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marchand_documents', function (Blueprint $table) {
            $table->id();

            // Relation avec le marchand
            $table->foreignId('marchand_id')->constrained('marchands')->onDelete('cascade');

            // Type de document
            $table->enum('type_document', [
                // Documents d'identité
                'cni_recto',                    // CNI recto
                'cni_verso',                    // CNI verso
                'passeport',                    // Passeport
                'permis_conduire',              // Permis de conduire
                'photo_avec_cni',               // Photo tenant la CNI

                // Documents d'entreprise
                'registre_commerce',            // Registre de commerce
                'statuts_entreprise',           // Statuts de l'entreprise
                'kbis',                        // Extrait Kbis (si applicable)
                'recepisse_declaration',        // Récépissé de déclaration
                'autorisation_exercice',        // Autorisation d'exercer

                // Documents financiers
                'rib_bancaire',                // RIB/IBAN
                'attestation_bancaire',         // Attestation bancaire
                'justificatif_mobile_money',    // Justificatif Mobile Money
                'bilan_comptable',             // Bilan comptable
                'declaration_fiscale',          // Déclaration fiscale

                // Documents d'adresse
                'justificatif_domicile',       // Justificatif de domicile
                'bail_commercial',             // Bail commercial
                'facture_electricite',         // Facture d'électricité
                'facture_eau',                 // Facture d'eau

                // Autres documents
                'photo_profil',                // Photo de profil
                'logo_entreprise',             // Logo de l'entreprise
                'certificat_formation',        // Certificat de formation
                'autre'                        // Autre document
            ]);

            // Informations du fichier
            $table->string('nom_original'); // Nom original du fichier
            $table->string('nom_stockage'); // Nom du fichier sur le serveur
            $table->string('chemin_fichier'); // Chemin complet du fichier
            $table->string('extension', 10); // Extension du fichier
            $table->unsignedBigInteger('taille_fichier'); // Taille en octets
            $table->string('mime_type', 100); // Type MIME
            $table->string('hash_fichier', 64)->nullable(); // Hash SHA256 pour vérifier l'intégrité

            // Statut de validation
            $table->enum('statut_validation', [
                'en_attente',       // Document soumis, en attente de vérification
                'en_cours',         // Document en cours de vérification
                'valide',           // Document validé
                'rejete',           // Document rejeté
                'expire',           // Document expiré
                'a_renouveler'      // Document à renouveler
            ])->default('en_attente');

            // Informations de validation
            $table->unsignedBigInteger('validateur_id')->nullable(); // ID de l'admin qui a validé
            $table->timestamp('date_validation')->nullable();
            $table->text('commentaires_validation')->nullable();
            $table->text('raison_rejet')->nullable();

            // Dates importantes
            $table->timestamp('date_upload')->useCurrent();
            $table->timestamp('date_expiration')->nullable(); // Date d'expiration du document
            $table->timestamp('date_derniere_modification')->nullable();

            // Métadonnées
            $table->boolean('est_obligatoire')->default(true); // Document obligatoire ou optionnel
            $table->boolean('est_confidentiel')->default(true); // Document confidentiel
            $table->integer('version')->default(1); // Version du document (si re-uploadé)
            $table->json('metadonnees')->nullable(); // Métadonnées additionnelles (OCR, etc.)

            // Informations de sécurité
            $table->boolean('est_crypte')->default(false); // Fichier crypté
            $table->string('cle_cryptage')->nullable(); // Clé de cryptage si applicable
            $table->string('adresse_ip_upload', 45)->nullable(); // IP lors de l'upload
            $table->text('user_agent_upload')->nullable(); // User agent lors de l'upload

            // Traitement automatique
            $table->boolean('ocr_effectue')->default(false); // OCR effectué
            $table->json('donnees_ocr')->nullable(); // Données extraites par OCR
            $table->decimal('score_qualite', 3, 2)->nullable(); // Score de qualité (0-1)
            $table->json('verifications_automatiques')->nullable(); // Résultats des vérifications auto

            $table->timestamps();

            // Index pour optimiser les requêtes avec noms personnalisés
            $table->index(['marchand_id', 'type_document'], 'md_marchand_type_idx');
            $table->index(['statut_validation', 'date_upload'], 'md_statut_upload_idx');
            $table->index(['est_obligatoire', 'statut_validation'], 'md_oblig_statut_idx');
            $table->index(['date_expiration'], 'md_expiration_idx');
            $table->index(['validateur_id', 'date_validation'], 'md_valid_date_idx');
            $table->unique(['marchand_id', 'type_document', 'version'], 'md_unique_type_version'); // Un seul document par type et version

            // Contraintes de clé étrangère
            $table->foreign('validateur_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marchand_documents');
    }
};
