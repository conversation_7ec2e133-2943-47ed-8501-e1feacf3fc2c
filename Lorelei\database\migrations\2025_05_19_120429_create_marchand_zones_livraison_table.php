<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marchand_zones_livraison', function (Blueprint $table) {
            $table->id();
            $table->foreignId('marchand_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('zone_livraison_id')->constrained('zones_livraison')->onDelete('cascade');
            $table->decimal('frais_livraison', 10, 2);
            $table->integer('delai_livraison_min')->comment('En jours');
            $table->integer('delai_livraison_max')->comment('En jours');
            $table->boolean('actif')->default(true);
            $table->timestamps();

            // Index pour améliorer les performances des requêtes
            $table->index('marchand_id');
            $table->index('zone_livraison_id');
            $table->index('actif');

            // Contrainte d'unicité pour éviter les doublons
            $table->unique(['marchand_id', 'zone_livraison_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marchand_zones_livraison');
    }
};
