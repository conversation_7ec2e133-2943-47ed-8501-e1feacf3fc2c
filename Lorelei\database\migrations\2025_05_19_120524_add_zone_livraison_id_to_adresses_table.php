<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('adresses', function (Blueprint $table) {
            $table->foreignId('zone_livraison_id')->nullable()->constrained('zones_livraison')->onDelete('set null');
            $table->index('zone_livraison_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('adresses', function (Blueprint $table) {
            $table->dropForeign(['zone_livraison_id']);
            $table->dropIndex(['zone_livraison_id']);
            $table->dropColumn('zone_livraison_id');
        });
    }
};
