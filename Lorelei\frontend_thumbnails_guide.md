# Guide d'utilisation des miniatures dans le frontend

Ce guide explique comment utiliser les miniatures dans votre application frontend Vue.js avec TypeScript.

## Table des matières

1. [Modèles TypeScript](#modèles-typescript)
2. [Types et utilitaires](#types-et-utilitaires)
3. [Composables Vue](#composables-vue)
4. [Exemples d'utilisation](#exemples-dutilisation)
5. [Bonnes pratiques](#bonnes-pratiques)

## Modèles TypeScript

### Produits

```typescript
import { Product } from '@/models/Product';

const product = new Product(
  'id',
  'Nom du produit',
  'slug',
  'CODE123',
  'Marque',
  'Description',
  29.99,
  'FCFA',
  null, // discountPrice
  null, // discountStartDate
  null, // discountEndDate
  'image.jpg',
  ['image1.jpg', 'image2.jpg'],
  'Catégorie',
  true,
  4.5,
  10,
  'Vendeur',
  ['main1.jpg', 'main2.jpg'], // mainImageUrls
  ['add1.jpg', 'add2.jpg'], // additionalImageUrls
  { // thumbnailUrls
    small: ['thumb_small_1.jpg', 'thumb_small_2.jpg'],
    medium: ['thumb_medium_1.jpg', 'thumb_medium_2.jpg'],
    large: ['thumb_large_1.jpg', 'thumb_large_2.jpg']
  }
);

// Accès aux miniatures
console.log(product.thumbnailUrls.medium[0]); // Première miniature moyenne
```

### Catégories

```typescript
import { Category } from '@/models/Category';

const category = new Category(
  'id',
  'Nom de la catégorie',
  'slug',
  'Description',
  'image.jpg',
  null, // parentId
  { fr: 'Nom FR', en: 'Name EN' }, // rawName
  { fr: 'Description FR', en: 'Description EN' }, // rawDescription
  { // thumbnailUrls
    small: 'thumb_small.jpg',
    medium: 'thumb_medium.jpg',
    large: 'thumb_large.jpg'
  }
);

// Accès aux miniatures
console.log(category.thumbnailUrls.medium); // Miniature moyenne
```

## Types et utilitaires

### Types disponibles

```typescript
import { 
  ThumbnailSize, 
  SingleThumbnailUrls, 
  MultipleThumbnailUrls,
  ThumbnailUtils
} from '@/types/thumbnails';

// Tailles disponibles
type ThumbnailSize = 'small' | 'medium' | 'large';

// Pour un élément unique (catégorie, bannière)
interface SingleThumbnailUrls {
  small: string | null;
  medium: string | null;
  large: string | null;
}

// Pour des éléments multiples (produits, reviews)
interface MultipleThumbnailUrls {
  small: string[];
  medium: string[];
  large: string[];
}
```

### Utilitaires

```typescript
// Obtenir une miniature avec fallback
const thumbnailUrl = ThumbnailUtils.getThumbnailOrOriginal(
  thumbnailUrls,
  'medium',
  originalUrl,
  0 // index pour les tableaux
);

// Vérifier si des miniatures existent
const hasThumbs = ThumbnailUtils.hasThumbnails(thumbnailUrls);

// Obtenir la meilleure taille disponible
const bestSize = ThumbnailUtils.getBestAvailableSize(thumbnailUrls, 'medium');

// Générer un srcset pour les images responsives
const srcSet = ThumbnailUtils.generateSrcSet(thumbnailUrls, 0);
```

## Composables Vue

### Composable générique

```vue
<script setup lang="ts">
import { useThumbnails } from '@/composables/useThumbnails';

const props = defineProps<{
  thumbnailUrls: MultipleThumbnailUrls;
  imageUrls: string[];
}>();

const {
  thumbnailUrl,
  hasThumbnailsComputed,
  srcSet,
  getThumbnail,
  generateSrcSet
} = useThumbnails(props.thumbnailUrls, props.imageUrls);

// Utilisation réactive
const mediumThumb = thumbnailUrl.value('medium', 0);
const imageSrcSet = srcSet.value(0);

// Utilisation non-réactive
const smallThumb = getThumbnail('small', 1);
</script>
```

### Composable pour produits

```vue
<script setup lang="ts">
import { useProductThumbnails } from '@/composables/useThumbnails';

const props = defineProps<{ product: Product }>();

const {
  getMainThumbnail,
  getAllThumbnails,
  getGalleryThumbnails
} = useProductThumbnails(props.product.thumbnailUrls, props.product.imageUrls);

// Image principale
const mainImage = getMainThumbnail('large');

// Toutes les miniatures moyennes
const allMediumThumbs = getAllThumbnails('medium');

// Images de la galerie (sans la première)
const galleryImages = getGalleryThumbnails('small');
</script>
```

### Composable pour catégories

```vue
<script setup lang="ts">
import { useCategoryThumbnails } from '@/composables/useThumbnails';

const props = defineProps<{ category: Category }>();

const {
  getCategoryThumbnail,
  getCardThumbnail,
  getHeaderThumbnail
} = useCategoryThumbnails(props.category.thumbnailUrls, props.category.imageUrl);

// Miniature pour carte
const cardImage = getCardThumbnail(); // small par défaut

// Miniature pour en-tête
const headerImage = getHeaderThumbnail(); // large par défaut
</script>
```

## Exemples d'utilisation

### Carte de produit avec miniatures

```vue
<template>
  <div class="product-card">
    <!-- Image principale avec srcset -->
    <img
      :src="mainThumbnail"
      :srcset="mainSrcSet"
      :alt="product.name"
      sizes="(max-width: 640px) 150px, (max-width: 1024px) 300px, 600px"
      loading="lazy"
    />
    
    <!-- Galerie de miniatures -->
    <div class="thumbnail-gallery">
      <img
        v-for="(thumb, index) in galleryThumbnails"
        :key="index"
        :src="thumb"
        :alt="`${product.name} - Image ${index + 2}`"
        class="gallery-thumb"
        @click="selectImage(index + 1)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useProductThumbnails } from '@/composables/useThumbnails';

const props = defineProps<{ product: Product }>();

const {
  getMainThumbnail,
  getGalleryThumbnails,
  generateSrcSet
} = useProductThumbnails(props.product.thumbnailUrls, props.product.imageUrls);

const mainThumbnail = computed(() => getMainThumbnail('medium'));
const galleryThumbnails = computed(() => getGalleryThumbnails('small'));
const mainSrcSet = computed(() => generateSrcSet(0));
</script>
```

### Image responsive avec fallback

```vue
<template>
  <picture>
    <!-- WebP avec miniatures -->
    <source
      v-if="hasThumbnails"
      :srcset="webpSrcSet"
      type="image/webp"
    />
    
    <!-- JPEG avec miniatures -->
    <source
      v-if="hasThumbnails"
      :srcset="jpegSrcSet"
      type="image/jpeg"
    />
    
    <!-- Fallback -->
    <img
      :src="fallbackImage"
      :alt="alt"
      class="responsive-image"
      loading="lazy"
    />
  </picture>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useThumbnails } from '@/composables/useThumbnails';

const props = defineProps<{
  thumbnailUrls: SingleThumbnailUrls;
  originalUrl: string;
  alt: string;
}>();

const { hasThumbnails, generateSrcSet, getThumbnail } = useThumbnails(
  props.thumbnailUrls,
  props.originalUrl
);

const webpSrcSet = computed(() => {
  // Générer srcset pour WebP (si supporté)
  return generateSrcSet().replace(/\.(jpg|jpeg|png)/g, '.webp');
});

const jpegSrcSet = computed(() => generateSrcSet());

const fallbackImage = computed(() => 
  getThumbnail('medium') || props.originalUrl
);
</script>
```

## Bonnes pratiques

### 1. Utilisation des tailles appropriées

```typescript
// Pour les cartes de produits
const cardImage = getThumbnail('small'); // 150x150

// Pour les pages de détail
const detailImage = getThumbnail('large'); // 600x600

// Pour les listes de catégories
const categoryIcon = getThumbnail('small'); // 100x100
```

### 2. Images responsives

```vue
<template>
  <img
    :src="getThumbnail('medium')"
    :srcset="generateSrcSet()"
    sizes="(max-width: 640px) 150px, (max-width: 1024px) 300px, 600px"
    loading="lazy"
    alt="Description"
  />
</template>
```

### 3. Gestion des erreurs

```vue
<template>
  <img
    :src="imageUrl"
    :alt="alt"
    @error="handleImageError"
    class="product-image"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps<{
  thumbnailUrls: MultipleThumbnailUrls;
  originalUrls: string[];
  alt: string;
}>();

const imageUrl = ref(getThumbnail('medium', 0));

const handleImageError = () => {
  // Fallback vers l'image originale
  imageUrl.value = props.originalUrls[0] || '/images/placeholder.jpg';
};
</script>
```

### 4. Lazy loading et performance

```vue
<template>
  <!-- Intersection Observer pour lazy loading -->
  <div ref="imageContainer" class="image-container">
    <img
      v-if="isVisible"
      :src="getThumbnail('medium')"
      :srcset="generateSrcSet()"
      :alt="alt"
      class="lazy-image"
    />
    <div v-else class="image-placeholder">
      <!-- Skeleton loader -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useIntersectionObserver } from '@vueuse/core';

const imageContainer = ref<HTMLElement>();
const isVisible = ref(false);

onMounted(() => {
  if (imageContainer.value) {
    useIntersectionObserver(
      imageContainer,
      ([{ isIntersecting }]) => {
        if (isIntersecting) {
          isVisible.value = true;
        }
      },
      { threshold: 0.1 }
    );
  }
});
</script>
```

### 5. Préchargement des images critiques

```typescript
// Précharger les images importantes
const preloadCriticalImages = (products: Product[]) => {
  products.slice(0, 6).forEach(product => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = product.thumbnailUrls.medium[0] || product.imageUrls[0];
    document.head.appendChild(link);
  });
};
```

Ce système de miniatures offre une solution complète et performante pour gérer les images dans votre application frontend, avec un support complet pour les images responsives, le lazy loading et les fallbacks.
