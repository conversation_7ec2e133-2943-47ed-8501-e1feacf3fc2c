# Stockage des images dans la base de données - Loʁelei Marketplace

Ce document explique comment les liens vers les images sont stockés dans la base de données et comment ils sont utilisés avec le système de miniatures.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Structure par modèle](#structure-par-modèle)
3. [Accesseurs et URLs](#accesseurs-et-urls)
4. [Intégration avec les miniatures](#intégration-avec-les-miniatures)
5. [Exemples d'utilisation](#exemples-dutilisation)

## Vue d'ensemble

Dans Loʁelei Marketplace, les images ne sont pas stockées directement dans la base de données. Seuls les **chemins relatifs** vers les fichiers sont sauvegardés. Cette approche offre plusieurs avantages :

- **Performance** : Base de données plus légère
- **Flexibilité** : Facilite la migration vers un CDN
- **Maintenance** : Gestion des fichiers indépendante de la base de données

## Structure par modèle

### 1. Produits (`produits` table)

**Champ en base** : `images` (JSON)

**Structure stockée** :
```json
[
  "products/1/01JSGYWWVB6BJK749JAPN8NXPF.jpg",
  "products/1/01JSGYWWVB6BJK749JAPN8NXPG.jpg"
]
```

**Accesseurs disponibles** :
- `image_urls` : URLs complètes des images originales
- `main_image_urls` : Les 2 premières images (pour affichage principal)
- `additional_image_urls` : Toutes les autres images
- `thumbnail_urls` : URLs des miniatures par taille

**Exemple de réponse API** :
```json
{
  "id": 123,
  "name": "Produit exemple",
  "images": [
    "products/1/01JSGYWWVB6BJK749JAPN8NXPF.jpg"
  ],
  "image_urls": [
    "http://localhost:8000/images/products/1/01JSGYWWVB6BJK749JAPN8NXPF.jpg"
  ],
  "thumbnail_urls": {
    "small": [
      "http://localhost:8000/images/thumbnail/products/1/small/01JSGYWWVB6BJK749JAPN8NXPF.jpg"
    ],
    "medium": [
      "http://localhost:8000/images/thumbnail/products/1/medium/01JSGYWWVB6BJK749JAPN8NXPF.jpg"
    ],
    "large": [
      "http://localhost:8000/images/thumbnail/products/1/large/01JSGYWWVB6BJK749JAPN8NXPF.jpg"
    ]
  }
}
```

### 2. Catégories (`categories` table)

**Champ en base** : `image_url` (string)

**Structure stockée** :
```
"categories/2/image.jpg"
```

**Accesseurs disponibles** :
- `full_image_url` : URL complète de l'image originale
- `thumbnail_urls` : URLs des miniatures par taille

**Exemple de réponse API** :
```json
{
  "id": 456,
  "nom": "Catégorie exemple",
  "image_url": "categories/2/image.jpg",
  "full_image_url": "http://localhost:8000/images/categories/2/image.jpg",
  "thumbnail_urls": {
    "small": "http://localhost:8000/images/thumbnail/categories/2/small/image.jpg",
    "medium": "http://localhost:8000/images/thumbnail/categories/2/medium/image.jpg",
    "large": "http://localhost:8000/images/thumbnail/categories/2/large/image.jpg"
  }
}
```

### 3. Bannières (`banners` table)

**Champ en base** : `image_url` (string)

**Structure stockée** :
```
"banners/ab/banner.jpg"
```

**Accesseurs disponibles** :
- `full_image_url` : URL complète de l'image originale
- `thumbnail_urls` : URLs des miniatures par taille

**Exemple de réponse API** :
```json
{
  "id": "01JSGYWWVB6BJK749JAPN8NXPF",
  "title": "Bannière exemple",
  "image_url": "banners/01/banner.jpg",
  "full_image_url": "http://localhost:8000/images/banners/01/banner.jpg",
  "thumbnail_urls": {
    "small": "http://localhost:8000/images/thumbnail/banners/01/small/banner.jpg",
    "medium": "http://localhost:8000/images/thumbnail/banners/01/medium/banner.jpg",
    "large": "http://localhost:8000/images/thumbnail/banners/01/large/banner.jpg"
  }
}
```

### 4. Reviews (`reviews` table)

**Champ en base** : `images` (JSON)

**Structure stockée** :
```json
[
  {
    "folder": "4",
    "name": "review_image.jpg"
  }
]
```

**Accesseurs disponibles** :
- `image_urls` : URLs complètes des images originales
- `thumbnail_urls` : URLs des miniatures par taille

**Exemple de réponse API** :
```json
{
  "id": 789,
  "comment": "Excellent produit !",
  "images": [
    {
      "folder": "4",
      "name": "review_image.jpg"
    }
  ],
  "image_urls": [
    "http://localhost:8000/images/reviews/4/review_image.jpg"
  ],
  "thumbnail_urls": {
    "small": [
      "http://localhost:8000/images/thumbnail/reviews/4/small/review_image.jpg"
    ],
    "medium": [
      "http://localhost:8000/images/thumbnail/reviews/4/medium/review_image.jpg"
    ],
    "large": [
      "http://localhost:8000/images/thumbnail/reviews/4/large/review_image.jpg"
    ]
  }
}
```

## Accesseurs et URLs

### Fonctionnement des accesseurs

Les accesseurs Laravel transforment automatiquement les chemins relatifs en URLs complètes :

```php
// Dans le modèle Produit
public function getImageUrlsAttribute(): array
{
    if (empty($this->images)) {
        return [];
    }

    // Transformer les chemins relatifs en URLs complètes
    return array_map(function ($image) {
        return url('/images/' . $image);
    }, $this->images);
}
```

### Gestion des dossiers

Chaque type de contenu utilise une structure de dossiers basée sur l'ID :

- **Produits** : `products/{folder_prefix}/` où `folder_prefix` = premier chiffre de l'ID
- **Catégories** : `categories/{folder_prefix}/` où `folder_prefix` = premier chiffre de l'ID
- **Bannières** : `banners/{folder_prefix}/` où `folder_prefix` = 2 premiers caractères de l'UUID
- **Reviews** : `reviews/{folder}/` où `folder` est défini dans les données JSON

## Intégration avec les miniatures

### Génération automatique

Lorsqu'une image est uploadée via Filament, le système :

1. **Stocke l'image originale** dans le dossier approprié
2. **Génère les miniatures** dans `thumbnail/{type}/{folder_prefix}/{size}/`
3. **Sauvegarde le chemin relatif** dans la base de données
4. **Expose les URLs** via les accesseurs

### Structure des miniatures

```
public/images/
├── products/1/image.jpg (original)
└── thumbnail/
    └── products/1/
        ├── small/image.jpg (150x150)
        ├── medium/image.jpg (300x300)
        └── large/image.jpg (600x600)
```

### Fallback automatique

Si une miniature n'existe pas, le système retourne automatiquement l'URL de l'image originale :

```php
// Dans ThumbnailHelper
if (file_exists($fullThumbnailPath)) {
    return url("images/{$thumbnailPath}");
}

// Fallback vers l'image originale
return url("images/{$imagePath}");
```

## Exemples d'utilisation

### Dans les contrôleurs API

```php
// Les accesseurs sont automatiquement inclus
$products = Produit::all();
return response()->json($products);

// Chaque produit aura automatiquement :
// - image_urls
// - thumbnail_urls
// - main_image_urls
// - additional_image_urls
```

### Dans les vues Blade

```blade
{{-- Utilisation des accesseurs --}}
@foreach($product->thumbnail_urls['medium'] as $thumbnail)
    <img src="{{ $thumbnail }}" alt="{{ $product->name }}">
@endforeach

{{-- Utilisation des helpers --}}
<img src="{{ product_thumbnail($product->images[0], 'medium') }}" alt="{{ $product->name }}">
```

### Dans React/Vue

```javascript
// Les données sont automatiquement disponibles via l'API
const ProductCard = ({ product }) => {
    return (
        <div>
            <img 
                src={product.thumbnail_urls.medium[0]} 
                alt={product.name}
            />
        </div>
    );
};
```

### Avec le CDN

Lorsque le CDN est activé, les URLs sont automatiquement transformées :

```php
// Sans CDN
"http://localhost:8000/images/thumbnail/products/1/medium/image.jpg"

// Avec CDN
"https://cdn.lorelei.com/thumbnail/products/1/medium/image.jpg"
```

## Avantages de cette approche

1. **Flexibilité** : Facilite la migration vers un CDN
2. **Performance** : Base de données légère, images servies efficacement
3. **Maintenance** : Gestion des fichiers indépendante
4. **Évolutivité** : Ajout facile de nouvelles tailles de miniatures
5. **Compatibilité** : Fonctionne avec ou sans CDN

## Migration et maintenance

### Commandes utiles

```bash
# Générer les miniatures pour les images existantes
php artisan thumbnails:generate

# Migrer vers le CDN
php artisan cdn:migrate

# Synchroniser les fichiers
php artisan cdn:sync
```

### Nettoyage

Pour nettoyer les images orphelines (non référencées en base) :

```bash
# Commande personnalisée à créer
php artisan images:cleanup
```

Cette approche garantit une gestion efficace et flexible des images dans votre marketplace, avec une intégration transparente entre le stockage en base de données, le système de miniatures et le CDN.
