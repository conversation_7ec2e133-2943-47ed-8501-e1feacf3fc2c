# Implémentation du Cache pour Lorrelei Marketplace

Ce document détaille la stratégie d'implémentation du cache pour optimiser les performances du marketplace Lorrelei, en utilisant Redis pour le cache de données et MeiliSearch pour l'indexation et la recherche de produits.

## Table des matières

1. [Architecture globale](#architecture-globale)
2. [Redis - Cache de données](#redis---cache-de-données)
   - [Installation et configuration](#installation-et-configuration-de-redis)
   - [Implémentation du cache produit](#implémentation-du-cache-produit)
   - [Cache des pages fréquemment visitées](#cache-des-pages-fréquemment-visitées)
   - [Invalidation du cache](#invalidation-du-cache-redis)
3. [MeiliSearch - Indexation et recherche](#meilisearch---indexation-et-recherche)
   - [Installation et configuration](#installation-et-configuration-de-meilisearch)
   - [Indexation des produits](#indexation-des-produits)
   - [Indexation des catégories](#indexation-des-catégories)
   - [Implémentation de la recherche](#implémentation-de-la-recherche)
4. [Automatisation et maintenance](#automatisation-et-maintenance)
   - [Interface d'administration](#interface-dadministration)
   - [Tâches planifiées (Cron Jobs)](#tâches-planifiées-cron-jobs)
   - [Écouteurs d'événements](#écouteurs-dévénements)
5. [Monitoring et optimisation](#monitoring-et-optimisation)

## Architecture globale

L'architecture de cache proposée repose sur deux technologies complémentaires :

- **Redis** : Utilisé pour le cache de données transactionnelles et de pages
  - Cache des détails de produits individuels
  - Cache des sessions utilisateurs
  - Cache des fragments HTML pour les composants statiques

- **MeiliSearch** : Utilisé pour l'indexation et la recherche
  - Indexation complète du catalogue produits
  - Indexation des catégories
  - Moteur de recherche pour les pages d'accueil, catégories et recherche

![Architecture du cache](https://mermaid.ink/img/pako:eNp1kU1PwzAMhv9KlBOgSf3YpGlw2A6cEBJiB8QhpDFrRZO0SjKkaeK_4_YDwQa-JH79-LFjn6A0FiGDvdmJN1NLVTrTWlU7JMgLVTcGK4JHp2qCB6cbp0vMCR5QNQQvRrUEuTJuR_BkVU6wVXWLBM9W7Qnu0BmCF9VgTvDq9vgfcIctwcZ0mDtV4-7jYzKdTjJYYIWqbXCwXMJoNJpABrNiOZ8tZovVKoNcm9JjMJxOp8NwGMFyMZ_NF-HweDwOhxEcjofDMRzG8fHr-3OVwQpL7_zNIFzHcRzFURTHcfT1_fOdwQYrb7yvHIZBEARhEIRBEHx9fH5ksEXtvfP-Jl3X67Rr0nWapun7y-trBjts0PvO-5t0XddpGqfpOk3T9O3p-TmDPbbB-87fDOIwDMMoDKMwDMO3x6enDA7YBh-8vxnEYRiGYRSGURiG4evDw0MGR_TB-5tBHIZhGEZhGIVhGL7c399ncAoXfXDBDVZYYqHKxmHvL1RZOqxU2TjfYqHKxvv-F5C95Qs)

## Redis - Cache de données

### Installation et configuration de Redis

1. **Installation de Redis**
   ```bash
   # Sur Ubuntu/Debian
   sudo apt update
   sudo apt install redis-server

   # Vérifier l'installation
   redis-cli ping
   # Devrait retourner "PONG"
   ```

2. **Configuration de base de Redis**
   ```bash
   # Éditer le fichier de configuration
   sudo nano /etc/redis/redis.conf

   # Paramètres recommandés
   maxmemory 2gb
   maxmemory-policy allkeys-lru
   ```

3. **Installation du client PHP pour Redis**
   ```bash
   composer require predis/predis
   ```

### Implémentation du cache produit avec gestion des variantes

Créer un service de cache pour les produits et leurs variantes :

```php
// app/Services/ProductCacheService.php
namespace App\Services;

use App\Models\Produit;
use App\Models\ProductVariant;
use Illuminate\Support\Facades\Redis;

class ProductCacheService
{
    private $expirationTime = 3600; // 1 heure en secondes
    private $variantExpirationTime = 7200; // 2 heures en secondes

    /**
     * Récupérer un produit depuis le cache ou la base de données
     */
    public function getProduct($productId)
    {
        $cacheKey = "product:{$productId}";

        // Vérifier si le produit est en cache
        if (Redis::exists($cacheKey)) {
            return json_decode(Redis::get($cacheKey), true);
        }

        // Récupérer depuis la base de données avec toutes les relations nécessaires
        $product = Produit::with([
                'categorie',
                'marchand',
                'attributs',
                'variants.attributs', // Inclure les attributs des variantes
                'reviews',
                'produitZonesLivraison'
            ])
            ->find($productId);

        if (!$product) {
            return null;
        }

        // Transformer le produit pour l'API
        $transformedProduct = $this->transformProduct($product);

        // Mettre en cache
        Redis::setex($cacheKey, $this->expirationTime, json_encode($transformedProduct));

        // Mettre en cache chaque variante individuellement
        foreach ($product->variants as $variant) {
            $this->cacheVariant($variant, $product);
        }

        return $transformedProduct;
    }

    /**
     * Récupérer une variante spécifique depuis le cache ou la base de données
     */
    public function getVariant($variantId, $productId = null)
    {
        $cacheKey = "variant:{$variantId}";

        // Vérifier si la variante est en cache
        if (Redis::exists($cacheKey)) {
            return json_decode(Redis::get($cacheKey), true);
        }

        // Si la variante n'est pas en cache, la récupérer depuis la base de données
        $variant = ProductVariant::with(['attributs'])
            ->find($variantId);

        if (!$variant) {
            return null;
        }

        // Si le productId n'est pas fourni, le récupérer depuis la variante
        if (!$productId) {
            $productId = $variant->produit_id;
        }

        // Récupérer le produit parent si nécessaire
        $product = null;
        if ($productId) {
            $productCacheKey = "product:{$productId}";
            if (Redis::exists($productCacheKey)) {
                $product = json_decode(Redis::get($productCacheKey), true);
            } else {
                $product = Produit::find($productId);
            }
        }

        // Mettre la variante en cache
        return $this->cacheVariant($variant, $product);
    }

    /**
     * Mettre en cache une variante de produit
     */
    private function cacheVariant($variant, $product = null)
    {
        $cacheKey = "variant:{$variant->id}";

        // Transformer la variante
        $transformedVariant = $this->transformVariant($variant, $product);

        // Mettre en cache
        Redis::setex($cacheKey, $this->variantExpirationTime, json_encode($transformedVariant));

        return $transformedVariant;
    }

    /**
     * Invalider le cache d'un produit et de ses variantes
     */
    public function invalidateProduct($productId)
    {
        // Récupérer le produit pour obtenir les IDs des variantes
        $product = Produit::with('variants')->find($productId);

        if ($product) {
            // Invalider le cache de chaque variante
            foreach ($product->variants as $variant) {
                $this->invalidateVariant($variant->id);
            }
        }

        // Invalider le cache du produit
        $cacheKey = "product:{$productId}";
        Redis::del($cacheKey);
    }

    /**
     * Invalider le cache d'une variante spécifique
     */
    public function invalidateVariant($variantId)
    {
        $cacheKey = "variant:{$variantId}";
        Redis::del($cacheKey);
    }

    /**
     * Transformer le produit pour le cache
     */
    private function transformProduct($product)
    {
        // Transformer les variantes
        $transformedVariants = [];
        foreach ($product->variants as $variant) {
            $transformedVariants[] = $this->transformVariant($variant, $product, false);
        }

        // Logique de transformation similaire à celle du contrôleur
        $transformedProduct = [
            'id' => $product->id,
            'nom' => $product->nom,
            'slug' => $product->slug,
            'description' => $product->description,
            'prix' => (float) $product->prix,
            'currency' => $product->currency,
            'stock' => (int) $product->stock,
            // ... autres propriétés du produit

            // Ajouter les variantes transformées
            'variants' => $transformedVariants,

            // Ajouter des informations agrégées sur les variantes
            'has_variants' => count($transformedVariants) > 0,
            'total_stock' => (int) $product->stock + array_sum(array_column($transformedVariants, 'stock')),
            'available_colors' => $this->extractUniqueAttributeValues($transformedVariants, 'couleur', 'nom'),
            'available_sizes' => $this->extractUniqueAttributeValues($transformedVariants, 'taille', 'valeur'),
            'available_materials' => $this->extractUniqueAttributeValues($transformedVariants, 'matiere', 'valeur'),
        ];

        return $transformedProduct;
    }

    /**
     * Transformer une variante pour le cache
     */
    private function transformVariant($variant, $product = null, $includeProductInfo = true)
    {
        $transformedVariant = [
            'id' => $variant->id,
            'sku' => $variant->sku,
            'prix_supplement' => (float) $variant->prix_supplement,
            'stock' => (int) $variant->stock,
            'images' => $variant->images ? json_decode($variant->images) : [],
            'attributs' => $variant->attributs->map(function ($attr) {
                return [
                    'id' => $attr->id,
                    'type' => $attr->type,
                    'nom' => $attr->nom,
                    'valeur' => $attr->valeur,
                    'code_couleur' => $attr->code_couleur,
                ];
            })->toArray(),
        ];

        // Ajouter le prix total si le produit parent est disponible
        if ($product) {
            $basePrice = is_object($product) ? $product->prix : $product['prix'];
            $transformedVariant['prix_total'] = (float) ($basePrice + $variant->prix_supplement);

            // Inclure des informations de base sur le produit parent si demandé
            if ($includeProductInfo) {
                $transformedVariant['product_info'] = [
                    'id' => is_object($product) ? $product->id : $product['id'],
                    'nom' => is_object($product) ? $product->nom : $product['nom'],
                    'slug' => is_object($product) ? $product->slug : $product['slug'],
                    'prix_base' => (float) $basePrice,
                ];
            }
        }

        return $transformedVariant;
    }

    /**
     * Extraire les valeurs uniques d'un type d'attribut spécifique à partir des variantes
     */
    private function extractUniqueAttributeValues($variants, $attributeType, $valueField)
    {
        $values = [];

        foreach ($variants as $variant) {
            foreach ($variant['attributs'] as $attr) {
                if ($attr['type'] === $attributeType && isset($attr[$valueField]) && $attr[$valueField]) {
                    $values[] = $attr[$valueField];
                }
            }
        }

        return array_values(array_unique($values));
    }
}
```

Utilisation dans le contrôleur :

```php
// app/Http/Controllers/Api/ProduitController.php
use App\Services\ProductCacheService;

class ProduitController extends Controller
{
    protected $productCacheService;

    public function __construct(ProductCacheService $productCacheService)
    {
        $this->productCacheService = $productCacheService;
    }

    public function show(string $id): JsonResponse
    {
        $product = $this->productCacheService->getProduct($id);

        if (!$product) {
            return response()->json(['error' => 'Product not found'], 404);
        }

        return response()->json($product);
    }

    // ...
}
```

### Cache des pages fréquemment visitées

Pour les pages complètes ou fragments HTML :

```php
// Exemple de middleware de cache pour les pages
namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Redis;

class PageCache
{
    public function handle($request, Closure $next)
    {
        // Ne pas mettre en cache pour les utilisateurs authentifiés
        if (auth()->check()) {
            return $next($request);
        }

        $url = $request->url();
        $cacheKey = "page:".md5($url);

        if (Redis::exists($cacheKey)) {
            return response(Redis::get($cacheKey));
        }

        $response = $next($request);

        // Mettre en cache seulement les réponses réussies
        if ($response->status() === 200) {
            Redis::setex($cacheKey, 1800, $response->getContent()); // 30 minutes
        }

        return $response;
    }
}
```

### Invalidation du cache Redis

Créer un trait pour les modèles qui nécessitent une invalidation de cache :

```php
// app/Traits/InvalidatesCache.php
namespace App\Traits;

use Illuminate\Support\Facades\Redis;

trait InvalidatesCache
{
    protected static function bootInvalidatesCache()
    {
        static::updated(function ($model) {
            $model->invalidateCache();
        });

        static::deleted(function ($model) {
            $model->invalidateCache();
        });
    }

    public function invalidateCache()
    {
        // Invalider le cache spécifique au modèle
        $cacheKey = strtolower(class_basename($this)).":".$this->id;
        Redis::del($cacheKey);

        // Invalider les caches de pages associés
        $this->invalidateRelatedPageCaches();
    }

    protected function invalidateRelatedPageCaches()
    {
        // À implémenter selon les besoins spécifiques
    }
}
```

## MeiliSearch - Indexation et recherche

### Installation et configuration de MeiliSearch

1. **Installation de MeiliSearch**
   ```bash
   # Télécharger et installer MeiliSearch
   curl -L https://install.meilisearch.com | sh

   # Lancer MeiliSearch
   ./meilisearch --master-key=votre_clé_master
   ```

2. **Installation du client PHP pour MeiliSearch**
   ```bash
   composer require meilisearch/meilisearch-php http-interop/http-factory-guzzle
   ```

3. **Configuration dans Laravel**
   ```php
   // config/services.php
   'meilisearch' => [
       'host' => env('MEILISEARCH_HOST', 'http://localhost:7700'),
       'key' => env('MEILISEARCH_KEY', null),
   ],
   ```

### Indexation des produits

Créer un service d'indexation pour MeiliSearch :

```php
// app/Services/MeiliSearchService.php
namespace App\Services;

use App\Models\Produit;
use App\Models\Categorie;
use MeiliSearch\Client;

class MeiliSearchService
{
    protected $client;

    public function __construct()
    {
        $this->client = new Client(config('services.meilisearch.host'), config('services.meilisearch.key'));
    }

    /**
     * Indexer tous les produits
     */
    public function indexAllProducts()
    {
        $productsIndex = $this->client->index('products');

        // Configurer les paramètres de recherche avec prise en compte des variantes
        $productsIndex->updateSettings([
            'searchableAttributes' => [
                'nom',
                'description',
                'categorie.nom',
                'marchand.nomEntreprise',
                'attributs.nom',
                'attributs.valeur',
                'variant_attribute_values', // Recherche dans toutes les valeurs d'attributs des variantes
                'variants.attributs.nom',
                'variants.attributs.valeur',
                'available_colors',
                'available_sizes',
                'available_materials'
            ],
            'filterableAttributes' => [
                'prix',
                'categorie_id',
                'marchand_id',
                'stock',
                'attributs.type',
                'has_variants',
                'available_colors',
                'available_sizes',
                'available_materials',
                'variants.prix_total',
                'variants.stock'
            ],
            'sortableAttributes' => [
                'prix',
                'created_at',
                'average_rating',
                'stock'
            ],
            'rankingRules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'sort',
                'exactness',
                'stock:desc' // Favoriser les produits en stock
            ]
        ]);

        // Récupérer et transformer les produits
        Produit::with(['categorie', 'marchand', 'attributs'])
            ->chunk(500, function ($products) use ($productsIndex) {
                $documentsToIndex = [];

                foreach ($products as $product) {
                    $documentsToIndex[] = $this->transformProductForIndexing($product);
                }

                $productsIndex->addDocuments($documentsToIndex);
            });

        return true;
    }

    /**
     * Indexer un seul produit
     */
    public function indexProduct($productId)
    {
        $productsIndex = $this->client->index('products');

        $product = Produit::with(['categorie', 'marchand', 'attributs'])
            ->find($productId);

        if (!$product) {
            return false;
        }

        $productsIndex->addDocuments([$this->transformProductForIndexing($product)]);

        return true;
    }

    /**
     * Transformer un produit pour l'indexation, incluant ses variantes
     */
    private function transformProductForIndexing($product)
    {
        // Charger les variantes si elles ne sont pas déjà chargées
        if (!$product->relationLoaded('variants')) {
            $product->load('variants.attributs');
        }

        // Extraire toutes les valeurs d'attributs des variantes pour la recherche
        $variantAttributeValues = [];
        $variantColors = [];
        $variantSizes = [];
        $variantMaterials = [];
        $allVariantAttributes = [];
        $totalStock = (int) $product->stock;

        // Traiter les variantes
        $variants = $product->variants->map(function ($variant) use (
            &$variantAttributeValues,
            &$variantColors,
            &$variantSizes,
            &$variantMaterials,
            &$allVariantAttributes,
            &$totalStock
        ) {
            // Ajouter le stock de la variante au stock total
            $totalStock += (int) $variant->stock;

            // Traiter les attributs de la variante
            $variantAttrs = $variant->attributs->map(function ($attr) use (
                &$variantAttributeValues,
                &$variantColors,
                &$variantSizes,
                &$variantMaterials,
                &$allVariantAttributes
            ) {
                // Ajouter la valeur à la liste des valeurs d'attributs pour la recherche
                if ($attr->nom) {
                    $variantAttributeValues[] = $attr->nom;
                }
                if ($attr->valeur) {
                    $variantAttributeValues[] = $attr->valeur;
                }

                // Catégoriser les attributs par type
                if ($attr->type === 'couleur' && $attr->nom) {
                    $variantColors[] = $attr->nom;
                } elseif ($attr->type === 'taille' && $attr->valeur) {
                    $variantSizes[] = $attr->valeur;
                } elseif ($attr->type === 'matiere' && $attr->valeur) {
                    $variantMaterials[] = $attr->valeur;
                }

                // Ajouter à la liste complète des attributs
                $allVariantAttributes[] = [
                    'id' => $attr->id,
                    'type' => $attr->type,
                    'nom' => $attr->nom,
                    'valeur' => $attr->valeur,
                    'code_couleur' => $attr->code_couleur,
                    'variant_id' => $variant->id
                ];

                return [
                    'id' => $attr->id,
                    'type' => $attr->type,
                    'nom' => $attr->nom,
                    'valeur' => $attr->valeur,
                    'code_couleur' => $attr->code_couleur,
                ];
            })->toArray();

            return [
                'id' => $variant->id,
                'sku' => $variant->sku,
                'prix_supplement' => (float) $variant->prix_supplement,
                'stock' => (int) $variant->stock,
                'images' => $variant->images ? json_decode($variant->images) : [],
                'attributs' => $variantAttrs,
                'prix_total' => (float) ($product->prix + $variant->prix_supplement)
            ];
        })->toArray();

        // Éliminer les doublons et convertir en tableaux
        $variantColors = array_values(array_unique($variantColors));
        $variantSizes = array_values(array_unique($variantSizes));
        $variantMaterials = array_values(array_unique($variantMaterials));

        return [
            'id' => $product->id,
            'nom' => $product->nom,
            'slug' => $product->slug,
            'description' => $product->description,
            'prix' => (float) $product->prix,
            'stock' => $totalStock, // Stock total incluant les variantes
            'categorie_id' => $product->categorie_id,
            'categorie' => $product->categorie ? [
                'id' => $product->categorie->id,
                'nom' => $product->categorie->nom,
                'slug' => $product->categorie->slug,
            ] : null,
            'marchand_id' => $product->marchand_id,
            'marchand' => $product->marchand ? [
                'id' => $product->marchand->id,
                'nomEntreprise' => $product->marchand->nomEntreprise,
            ] : null,
            // Attributs du produit principal
            'attributs' => $product->attributs->map(function ($attribut) {
                return [
                    'id' => $attribut->id,
                    'type' => $attribut->type,
                    'nom' => $attribut->nom,
                    'valeur' => $attribut->valeur,
                    'code_couleur' => $attribut->code_couleur,
                ];
            })->toArray(),
            // Inclure toutes les variantes
            'variants' => $variants,
            // Attributs agrégés de toutes les variantes pour la recherche
            'variant_attributs' => $allVariantAttributes,
            'variant_attribute_values' => $variantAttributeValues,
            'available_colors' => $variantColors,
            'available_sizes' => $variantSizes,
            'available_materials' => $variantMaterials,
            'has_variants' => count($variants) > 0,
            'images' => $product->images ? json_decode($product->images) : [],
            'discount_price' => (float) $product->discount_price,
            'discount_start_date' => $product->discount_start_date,
            'discount_end_date' => $product->discount_end_date,
            'average_rating' => (float) $product->average_rating,
            'reviews_count' => (int) $product->reviews_count,
            'created_at' => $product->created_at->timestamp,
            'updated_at' => $product->updated_at->timestamp,
        ];
    }
}
```

### Indexation des catégories

Ajouter l'indexation des catégories au service MeiliSearch :

```php
// Dans app/Services/MeiliSearchService.php

/**
 * Indexer toutes les catégories
 */
public function indexAllCategories()
{
    $categoriesIndex = $this->client->index('categories');

    // Configurer les paramètres de recherche
    $categoriesIndex->updateSettings([
        'searchableAttributes' => [
            'nom',
            'description'
        ],
        'filterableAttributes' => [
            'parent_id',
            'actif'
        ],
        'sortableAttributes' => [
            'ordre',
            'created_at'
        ]
    ]);

    // Récupérer et transformer les catégories
    Categorie::with('parent')
        ->chunk(500, function ($categories) use ($categoriesIndex) {
            $documentsToIndex = [];

            foreach ($categories as $category) {
                $documentsToIndex[] = [
                    'id' => $category->id,
                    'nom' => $category->nom,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'parent_id' => $category->parent_id,
                    'parent' => $category->parent ? [
                        'id' => $category->parent->id,
                        'nom' => $category->parent->nom,
                    ] : null,
                    'actif' => (bool) $category->actif,
                    'ordre' => (int) $category->ordre,
                    'created_at' => $category->created_at->timestamp,
                    'updated_at' => $category->updated_at->timestamp,
                ];
            }

            $categoriesIndex->addDocuments($documentsToIndex);
        });

    return true;
}

/**
 * Indexer une seule catégorie
 */
public function indexCategory($categoryId)
{
    $categoriesIndex = $this->client->index('categories');

    $category = Categorie::with('parent')
        ->find($categoryId);

    if (!$category) {
        return false;
    }

    $categoriesIndex->addDocuments([[
        'id' => $category->id,
        'nom' => $category->nom,
        'slug' => $category->slug,
        'description' => $category->description,
        'parent_id' => $category->parent_id,
        'parent' => $category->parent ? [
            'id' => $category->parent->id,
            'nom' => $category->parent->nom,
        ] : null,
        'actif' => (bool) $category->actif,
        'ordre' => (int) $category->ordre,
        'created_at' => $category->created_at->timestamp,
        'updated_at' => $category->updated_at->timestamp,
    ]]);

    return true;
}
```

### Implémentation de la recherche

Créer un contrôleur pour la recherche avec MeiliSearch :

```php
// app/Http/Controllers/Api/SearchController.php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use MeiliSearch\Client;

class SearchController extends Controller
{
    protected $client;

    public function __construct()
    {
        $this->client = new Client(config('services.meilisearch.host'), config('services.meilisearch.key'));
    }

    /**
     * Recherche de produits
     */
    public function searchProducts(Request $request)
    {
        $query = $request->input('q', '');
        $filters = $this->buildFilters($request);
        $sort = $request->input('sort');
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 20);

        $productsIndex = $this->client->index('products');

        $searchParams = [
            'filters' => $filters,
            'limit' => $limit,
            'offset' => ($page - 1) * $limit,
        ];

        if ($sort) {
            $searchParams['sort'] = [$sort];
        }

        $results = $productsIndex->search($query, $searchParams);

        return response()->json([
            'hits' => $results->getHits(),
            'total' => $results->getHitsCount(),
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($results->getHitsCount() / $limit),
        ]);
    }

    /**
     * Construire les filtres pour MeiliSearch, incluant les filtres sur les variantes
     */
    private function buildFilters(Request $request)
    {
        $filterParts = [];

        // Filtre par catégorie
        if ($request->has('category_id')) {
            $filterParts[] = 'categorie_id = ' . $request->input('category_id');
        }

        // Filtre par marchand
        if ($request->has('merchant_id')) {
            $filterParts[] = 'marchand_id = ' . $request->input('merchant_id');
        }

        // Filtre par fourchette de prix
        if ($request->has('price_min') && $request->has('price_max')) {
            $priceMin = $request->input('price_min');
            $priceMax = $request->input('price_max');

            // Filtrer à la fois le prix du produit principal et les prix des variantes
            $filterParts[] = '(prix >= ' . $priceMin . ' AND prix <= ' . $priceMax . ') OR ' .
                             '(variants.prix_total >= ' . $priceMin . ' AND variants.prix_total <= ' . $priceMax . ')';
        } else if ($request->has('price_min')) {
            $priceMin = $request->input('price_min');
            $filterParts[] = 'prix >= ' . $priceMin . ' OR variants.prix_total >= ' . $priceMin;
        } else if ($request->has('price_max')) {
            $priceMax = $request->input('price_max');
            $filterParts[] = 'prix <= ' . $priceMax . ' OR variants.prix_total <= ' . $priceMax;
        }

        // Filtre par disponibilité
        if ($request->has('in_stock') && $request->input('in_stock') === 'true') {
            $filterParts[] = 'stock > 0 OR variants.stock > 0';
        }

        // Filtre par couleur
        if ($request->has('color')) {
            $color = $request->input('color');
            $filterParts[] = 'available_colors = "' . $color . '"';
        }

        // Filtre par taille
        if ($request->has('size')) {
            $size = $request->input('size');
            $filterParts[] = 'available_sizes = "' . $size . '"';
        }

        // Filtre par matière
        if ($request->has('material')) {
            $material = $request->input('material');
            $filterParts[] = 'available_materials = "' . $material . '"';
        }

        // Filtre pour produits avec variantes uniquement
        if ($request->has('has_variants') && $request->input('has_variants') === 'true') {
            $filterParts[] = 'has_variants = true';
        }

        // Filtre pour produits sans variantes uniquement
        if ($request->has('has_variants') && $request->input('has_variants') === 'false') {
            $filterParts[] = 'has_variants = false';
        }

        return implode(' AND ', $filterParts);
    }

    /**
     * Recherche de catégories
     */
    public function searchCategories(Request $request)
    {
        $query = $request->input('q', '');
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 20);

        $categoriesIndex = $this->client->index('categories');

        $results = $categoriesIndex->search($query, [
            'filters' => 'actif = true',
            'limit' => $limit,
            'offset' => ($page - 1) * $limit,
        ]);

        return response()->json([
            'hits' => $results->getHits(),
            'total' => $results->getHitsCount(),
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($results->getHitsCount() / $limit),
        ]);
    }
}
```

## Automatisation et maintenance

### Interface d'administration

Créer une interface dans le back-office pour gérer l'indexation :

```php
// app/Http/Controllers/Admin/IndexationController.php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\MeiliSearchService;

class IndexationController extends Controller
{
    protected $meiliSearchService;

    public function __construct(MeiliSearchService $meiliSearchService)
    {
        $this->meiliSearchService = $meiliSearchService;
    }

    /**
     * Afficher la page d'indexation
     */
    public function index()
    {
        return view('admin.indexation.index');
    }

    /**
     * Indexer tous les produits
     */
    public function indexAllProducts(Request $request)
    {
        try {
            $this->meiliSearchService->indexAllProducts();
            return response()->json(['success' => true, 'message' => 'Tous les produits ont été indexés avec succès.']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Erreur lors de l\'indexation : ' . $e->getMessage()], 500);
        }
    }

    /**
     * Indexer toutes les catégories
     */
    public function indexAllCategories(Request $request)
    {
        try {
            $this->meiliSearchService->indexAllCategories();
            return response()->json(['success' => true, 'message' => 'Toutes les catégories ont été indexées avec succès.']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Erreur lors de l\'indexation : ' . $e->getMessage()], 500);
        }
    }

    /**
     * Indexer un produit spécifique
     */
    public function indexProduct(Request $request, $id)
    {
        try {
            $this->meiliSearchService->indexProduct($id);
            return response()->json(['success' => true, 'message' => 'Produit indexé avec succès.']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Erreur lors de l\'indexation : ' . $e->getMessage()], 500);
        }
    }
}
```

### Tâches planifiées (Cron Jobs)

Configurer des tâches planifiées pour l'indexation automatique :

```php
// app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    // Indexer tous les produits chaque jour à 3h du matin
    $schedule->command('indexation:products')->dailyAt('03:00');

    // Indexer toutes les catégories chaque jour à 2h30 du matin
    $schedule->command('indexation:categories')->dailyAt('02:30');

    // Nettoyer le cache Redis des produits non consultés depuis plus de 7 jours
    $schedule->command('cache:prune-products')->weekly();
}
```

Créer les commandes correspondantes :

```php
// app/Console/Commands/IndexProducts.php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MeiliSearchService;

class IndexProducts extends Command
{
    protected $signature = 'indexation:products';
    protected $description = 'Indexer tous les produits dans MeiliSearch';

    protected $meiliSearchService;

    public function __construct(MeiliSearchService $meiliSearchService)
    {
        parent::__construct();
        $this->meiliSearchService = $meiliSearchService;
    }

    public function handle()
    {
        $this->info('Début de l\'indexation des produits...');

        try {
            $this->meiliSearchService->indexAllProducts();
            $this->info('Indexation des produits terminée avec succès.');
        } catch (\Exception $e) {
            $this->error('Erreur lors de l\'indexation des produits : ' . $e->getMessage());
        }
    }
}
```

### Écouteurs d'événements

Configurer des écouteurs d'événements pour l'indexation automatique lors des modifications :

```php
// app/Providers/EventServiceProvider.php
protected $listen = [
    'App\Events\ProductCreated' => [
        'App\Listeners\IndexProduct',
    ],
    'App\Events\ProductUpdated' => [
        'App\Listeners\IndexProduct',
        'App\Listeners\InvalidateProductCache',
    ],
    'App\Events\CategoryCreated' => [
        'App\Listeners\IndexCategory',
    ],
    'App\Events\CategoryUpdated' => [
        'App\Listeners\IndexCategory',
    ],
];
```

Créer les événements et les écouteurs :

```php
// app/Events/ProductCreated.php
namespace App\Events;

use Illuminate\Foundation\Events\Dispatchable;
use App\Models\Produit;

class ProductCreated
{
    use Dispatchable;

    public $product;

    public function __construct(Produit $product)
    {
        $this->product = $product;
    }
}

// app/Listeners/IndexProduct.php
namespace App\Listeners;

use App\Events\ProductCreated;
use App\Events\ProductUpdated;
use App\Services\MeiliSearchService;

class IndexProduct
{
    protected $meiliSearchService;

    public function __construct(MeiliSearchService $meiliSearchService)
    {
        $this->meiliSearchService = $meiliSearchService;
    }

    public function handle($event)
    {
        $this->meiliSearchService->indexProduct($event->product->id);
    }
}
```

## Monitoring et optimisation

Pour surveiller les performances du cache et de l'indexation :

1. **Monitoring Redis**
   - Utiliser Redis Commander ou RedisInsight pour visualiser les données en cache
   - Surveiller l'utilisation de la mémoire avec `redis-cli info memory`

2. **Monitoring MeiliSearch**
   - Utiliser l'interface web de MeiliSearch (http://localhost:7700)
   - Surveiller les logs pour détecter les problèmes d'indexation

3. **Optimisation continue**
   - Ajuster les TTL (Time To Live) des caches Redis en fonction de l'utilisation
   - Affiner les paramètres de recherche de MeiliSearch pour améliorer la pertinence
   - Mettre en place des métriques pour identifier les produits les plus consultés et optimiser leur cache
