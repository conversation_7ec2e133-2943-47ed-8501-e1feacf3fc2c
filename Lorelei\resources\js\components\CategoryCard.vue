<template>
  <div class="category-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 group">
    <!-- Image de la catégorie -->
    <div class="relative aspect-square overflow-hidden">
      <img
        :src="categoryThumbnail"
        :srcset="categorySrcSet"
        :alt="categoryName"
        class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        sizes="(max-width: 640px) 100px, (max-width: 1024px) 200px, 400px"
        loading="lazy"
      />
      
      <!-- Overlay avec gradient -->
      <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      
      <!-- Nom de la catégorie en overlay -->
      <div class="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
        <h3 class="text-lg font-semibold">{{ categoryName }}</h3>
        <p v-if="categoryDescription" class="text-sm opacity-90 line-clamp-2">
          {{ categoryDescription }}
        </p>
      </div>
    </div>

    <!-- Informations de la catégorie -->
    <div class="p-4">
      <h3 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200">
        {{ categoryName }}
      </h3>
      
      <p v-if="categoryDescription" class="text-sm text-gray-600 mb-3 line-clamp-2">
        {{ categoryDescription }}
      </p>

      <!-- Sous-catégories -->
      <div v-if="category.hasChildren()" class="mb-3">
        <div class="flex flex-wrap gap-1">
          <span
            v-for="child in category.children.slice(0, 3)"
            :key="child.id"
            class="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
          >
            {{ child.getTranslatedName(currentLocale) }}
          </span>
          <span
            v-if="category.children.length > 3"
            class="inline-block bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full"
          >
            +{{ category.children.length - 3 }}
          </span>
        </div>
      </div>

      <!-- Niveau de la catégorie -->
      <div class="flex items-center justify-between text-sm text-gray-500">
        <span v-if="category.isSubcategory()">
          {{ $t('category.subcategory') }}
        </span>
        <span v-else>
          {{ $t('category.mainCategory') }}
        </span>
        
        <span v-if="category.hasChildren()">
          {{ $t('category.subcategoriesCount', { count: category.children.length }) }}
        </span>
      </div>
    </div>

    <!-- Action au clic -->
    <div class="absolute inset-0 cursor-pointer" @click="navigateToCategory"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Category } from '@/models/Category';
import { useCategoryThumbnails } from '@/composables/useThumbnails';

interface Props {
  category: Category;
  currentLocale?: string;
}

const props = withDefaults(defineProps<Props>(), {
  currentLocale: 'fr'
});

// Utilisation du composable pour les miniatures
const {
  getCategoryThumbnail,
  generateSrcSet
} = useCategoryThumbnails(props.category.thumbnailUrls, props.category.imageUrl);

// Miniature de la catégorie
const categoryThumbnail = computed(() => getCategoryThumbnail('medium'));

// SrcSet pour l'image
const categorySrcSet = computed(() => generateSrcSet());

// Nom traduit de la catégorie
const categoryName = computed(() => 
  props.category.getTranslatedName(props.currentLocale)
);

// Description traduite de la catégorie
const categoryDescription = computed(() => 
  props.category.getTranslatedDescription(props.currentLocale)
);

// Méthodes
const navigateToCategory = () => {
  // Logique de navigation vers la catégorie
  console.log('Navigation vers la catégorie:', props.category.slug);
  // Exemple avec Vue Router :
  // router.push({ name: 'category', params: { slug: props.category.slug } });
};
</script>

<style scoped>
.category-card {
  @apply relative;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}
</style>
