import React from 'react';
import { ShoppingBag, Trash2, Plus, Minus } from 'lucide-react';
import { useCart } from '@/contexts/CartContext';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { She<PERSON>, SheetContent, SheetHeader, SheetTitle, SheetFooter } from '@/components/ui/sheet';
import { Separator } from '@/components/ui/separator';
import { useTranslation } from '@/hooks/use-translation';

/**
 * Composant de barre latérale pour afficher le contenu du panier
 */
export default function CartSidebar() {
    const { translate } = useTranslation();
    const {
        items,
        removeItem,
        updateQuantity,
        subtotal,
        formattedSubtotal,
        deliveryFees,
        formattedDeliveryFees,
        grandTotal,
        formattedGrandTotal,
        itemCount,
        isCartOpen,
        setCartOpen
    } = useCart();

    /**
     * Ferme la barre latérale du panier
     */
    const closeCart = () => {
        setCartOpen(false);
    };

    /**
     * Génère une clé unique pour un produit basée sur ses attributs
     *
     * @param product - Le produit pour lequel générer une clé
     * @returns Une chaîne représentant la combinaison unique d'attributs
     */
    const getProductVariantKey = (product: any): string => {
        // Extraire les attributs de couleur, taille et matière
        const colorAttr = product.attributes.find((a: any) => a.type === 'couleur');
        const sizeAttr = product.attributes.find((a: any) => a.type === 'taille');
        const materialAttr = product.attributes.find((a: any) => a.type === 'matiere');

        // Créer une chaîne qui représente la combinaison unique d'attributs
        let variantKey = product.id;

        if (colorAttr && 'nom' in colorAttr) variantKey += `-color:${colorAttr.nom}`;
        if (sizeAttr && 'valeur' in sizeAttr) variantKey += `-size:${sizeAttr.valeur}`;
        if (materialAttr && 'valeur' in materialAttr) variantKey += `-material:${materialAttr.valeur}`;

        return variantKey;
    };

    /**
     * Augmente la quantité d'un produit dans le panier
     *
     * @param product - Le produit
     * @param currentQuantity - Quantité actuelle
     */
    const increaseQuantity = (product: any, currentQuantity: number) => {
        const variantKey = getProductVariantKey(product);
        updateQuantity(product.id, currentQuantity + 1, variantKey);
    };

    /**
     * Diminue la quantité d'un produit dans le panier
     *
     * @param product - Le produit
     * @param currentQuantity - Quantité actuelle
     */
    const decreaseQuantity = (product: any, currentQuantity: number) => {
        const variantKey = getProductVariantKey(product);
        if (currentQuantity > 1) {
            updateQuantity(product.id, currentQuantity - 1, variantKey);
        } else {
            removeItem(product.id, variantKey);
        }
    };

    /**
     * Vérifie si tous les produits du panier proviennent du même marchand
     *
     * @returns {boolean} true si tous les produits ont le même marchand, false sinon
     */
    const allProductsFromSameMerchant = (): boolean => {
        if (items.length <= 1) return true;

        const firstSeller = items[0].product.seller;
        return items.every(item => item.product.seller === firstSeller);
    };

    /**
     * Obtient le nom du marchand commun si tous les produits viennent du même marchand
     *
     * @returns {string|null} Le nom du marchand commun ou null
     */
    const getCommonMerchantName = (): string | null => {
        if (items.length === 0) return null;
        if (allProductsFromSameMerchant()) {
            return items[0].product.seller;
        }
        return null;
    };

    /**
     * Calcule les frais de livraison totaux par marchand
     *
     * @returns {Object} Un objet avec les frais de livraison par marchand
     */
    const getDeliveryFeesByMerchant = () => {
        const feesByMerchant: Record<string, {
            fees: number,
            merchantName: string,
            zoneName: string | undefined
        }> = {};

        items.forEach(item => {
            if (item.deliveryInfo) {
                const sellerId = item.product.seller || 'unknown';

                if (!feesByMerchant[sellerId]) {
                    feesByMerchant[sellerId] = {
                        fees: 0,
                        merchantName: item.product.seller || translate('common.unknown_merchant'),
                        zoneName: item.deliveryInfo.zone_nom
                    };
                }

                feesByMerchant[sellerId].fees += item.getDeliveryFees();
            }
        });

        return feesByMerchant;
    };

    // Vérifier si tous les produits viennent du même marchand
    const sameMerchant = allProductsFromSameMerchant();

    // Obtenir les frais de livraison par marchand
    const deliveryFeesByMerchant = getDeliveryFeesByMerchant();

    return (
        <Sheet open={isCartOpen} onOpenChange={setCartOpen}>
            <SheetContent className="flex w-full flex-col p-0 sm:max-w-md">
                <SheetHeader className="border-b px-6 py-4">
                    <SheetTitle className="text-left"> {translate('pages.cart.header')} ({itemCount})</SheetTitle>
                </SheetHeader>

                {/* Corps */}
                {items.length === 0 ? (
                    <div className="flex flex-1 flex-col items-center justify-center p-6">
                        <ShoppingBag className="mb-4 h-16 w-16 text-muted-foreground/50" />
                        <p className="mb-2 text-lg font-medium"> {translate('pages.cart.empty')}</p>
                        <p className="mb-4 text-center text-sm text-muted-foreground">
                            {translate('pages.cart.empty_description')}
                        </p>
                        <Button onClick={closeCart} variant="outline">
                            {translate('pages.cart.continue_shopping')}
                        </Button>
                    </div>
                ) : (
                    <>
                        {/* Liste des produits */}
                        <ScrollArea className="flex-1">
                            <div className="px-6 py-2">
                                {items.map((item) => (
                                    <div key={item.product.id} className="py-4">
                                        <div className="flex items-start gap-4">
                                            {/* Image du produit */}
                                            <div className="h-20 w-20 flex-shrink-0 overflow-hidden rounded-md border bg-muted/50">
                                                <img
                                                    src={item.product.imageUrl}
                                                    alt={item.product.name}
                                                    className="h-full w-full object-cover"
                                                />
                                            </div>

                                            {/* Détails du produit */}
                                            <div className="flex flex-1 flex-col">
                                                <div className="flex justify-between">
                                                    <h3 className="text-sm font-medium">
                                                        <Link
                                                            href={route('product', { productSlug: item.product.slug })}
                                                            className="hover:text-primary hover:underline"
                                                        >
                                                            {item.product.name}
                                                        </Link>
                                                    </h3>
                                                    <p className="text-sm font-medium">
                                                        {item.formattedSubtotal()}
                                                    </p>
                                                </div>

                                                {/* Affichage des attributs sélectionnés */}
                                                <div className="mt-1 flex flex-wrap gap-1">
                                                    {item.product.attributes
                                                        .filter(attr =>
                                                            (attr.type === 'couleur' && 'nom' in attr) ||
                                                            (attr.type === 'taille' && 'valeur' in attr) ||
                                                            (attr.type === 'matiere' && 'valeur' in attr)
                                                        )
                                                        .map((attr, index) => {
                                                            let label = '';
                                                            let value = '';

                                                            if (attr.type === 'couleur' && 'nom' in attr) {
                                                                label = translate('common.color');
                                                                value = attr.nom;

                                                                // Pour les attributs de couleur, on affiche un indicateur visuel
                                                                return (
                                                                    <span
                                                                        key={index}
                                                                        className="inline-flex items-center rounded-md bg-primary/10 px-1.5 py-0.5 text-[10px] font-medium text-primary"
                                                                    >
                                                                        {/* Afficher l'image de la couleur ou un carré de couleur */}
                                                                        {'with_image' in attr && attr.with_image && 'color_image' in attr && attr.color_image ? (
                                                                            <img
                                                                                src={attr.color_image}
                                                                                alt={value}
                                                                                className="mr-1 h-3 w-3 rounded-full object-cover"
                                                                            />
                                                                        ) : (
                                                                            <span
                                                                                className="mr-1 inline-block h-3 w-3 rounded-full border"
                                                                                style={{ backgroundColor: 'code' in attr ? attr.code : '#CCCCCC' }}
                                                                            ></span>
                                                                        )}
                                                                        {label}: {value}
                                                                    </span>
                                                                );
                                                            } else if (attr.type === 'taille' && 'valeur' in attr) {
                                                                label = translate('common.size');
                                                                value = attr.valeur;
                                                            } else if (attr.type === 'matiere' && 'valeur' in attr) {
                                                                label = translate('common.material');
                                                                value = attr.valeur;
                                                            }

                                                            if (!label || !value) return null;

                                                            // Pour les autres types d'attributs (taille, matière, etc.)
                                                            return (
                                                                <span
                                                                    key={index}
                                                                    className="inline-flex items-center rounded-md bg-primary/10 px-1.5 py-0.5 text-[10px] font-medium text-primary"
                                                                >
                                                                    {label}: {value}
                                                                </span>
                                                            );
                                                        })
                                                    }
                                                </div>

                                                {/* Contrôles de quantité et suppression */}
                                                <div className="mt-2 flex items-center justify-between">
                                                    <div className="flex items-center rounded-md border">
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            onClick={() => decreaseQuantity(item.product, item.quantity)}
                                                            className="h-8 w-8 rounded-none"
                                                            aria-label="Diminuer la quantité"
                                                        >
                                                            <Minus className="h-3 w-3" />
                                                        </Button>
                                                        <span className="w-8 text-center text-sm">{item.quantity}</span>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            onClick={() => increaseQuantity(item.product, item.quantity)}
                                                            className="h-8 w-8 rounded-none"
                                                            aria-label="Augmenter la quantité"
                                                        >
                                                            <Plus className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => removeItem(item.product.id, getProductVariantKey(item.product))}
                                                        className="text-muted-foreground hover:text-destructive"
                                                        aria-label="Supprimer du panier"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                        <Separator className="mt-4" />
                                    </div>
                                ))}
                            </div>
                        </ScrollArea>

                        {/* Pied de page avec sous-total, frais de livraison, total et boutons */}
                        <SheetFooter className="flex-col gap-2 border-t px-6 py-4">
                            {/* Résumé du panier */}
                            <div className="space-y-2 py-2">
                                {/* Sous-total */}
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">{translate('pages.cart.subtotal')}</span>
                                    <span className="text-sm font-medium">{formattedSubtotal}</span>
                                </div>

                                {/* Frais de livraison */}
                                {Object.keys(deliveryFeesByMerchant).length > 0 ? (
                                    <>
                                        {Object.entries(deliveryFeesByMerchant).map(([merchantId, info], index) => (
                                            <div key={merchantId} className="space-y-1">
                                                <div className="flex items-center justify-between">
                                                    <span className="text-sm text-muted-foreground">
                                                        {sameMerchant
                                                            ? translate('pages.cart.delivery_fees')
                                                            : `${translate('pages.cart.delivery_fees')} (${info.merchantName})`}
                                                    </span>
                                                    <span className="text-sm font-medium">
                                                        {info.fees > 0
                                                            ? `${info.fees.toLocaleString('fr-FR', { minimumFractionDigits: 0, maximumFractionDigits: 0 })} FCFA`
                                                            : translate('pages.cart.calculate_at_next_step')}
                                                    </span>
                                                </div>
                                                {info.zoneName && (
                                                    <div className="flex items-center justify-end">
                                                        <span className="text-xs text-muted-foreground">
                                                            {translate('common.zone')}: {info.zoneName}
                                                        </span>
                                                    </div>
                                                )}
                                                {index < Object.entries(deliveryFeesByMerchant).length - 1 && (
                                                    <Separator className="my-1" />
                                                )}
                                            </div>
                                        ))}
                                    </>
                                ) : (
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-muted-foreground">{translate('pages.cart.delivery_fees')}</span>
                                        <span className="text-sm font-medium">{formattedDeliveryFees}</span>
                                    </div>
                                )}

                                {/* Séparateur */}
                                <Separator className="my-2" />

                                {/* Total */}
                                <div className="flex items-center justify-between">
                                    <span className="text-base font-medium">{translate('pages.cart.total')}</span>
                                    <span className="text-lg font-semibold">{formattedGrandTotal}</span>
                                </div>
                            </div>

                            {/* Boutons */}
                            <Button asChild className="w-full">
                                <Link href={route('cart')}>{translate('pages.cart.go_to_cart')} </Link>
                            </Button>
                            <Button
                                variant="outline"
                                className="w-full"
                                onClick={closeCart}
                            >
                                {translate('pages.cart.continue_shopping')}
                            </Button>
                        </SheetFooter>
                    </>
                )}
            </SheetContent>
        </Sheet>
    );
}
