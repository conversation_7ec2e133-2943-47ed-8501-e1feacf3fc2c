import React, { useState, useEffect } from 'react';
import { useDelivery } from '@/contexts/DeliveryContext';
import { Product } from '@/models/Product';
import { Truck, AlertCircle, CheckCircle, XCircle, Loader2, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/hooks/use-translation';
import { DeliveryAvailabilityData, DeliveryAvailabilityWithChildrenData, ZoneLivraisonData } from '@/services/zoneLivraisonService';
import { formatPrice } from '@/utils/format';

interface DeliveryInfoProps {
  product: Product;
  className?: string;
  showIcon?: boolean;
  compact?: boolean;
}

/**
 * Composant pour afficher les informations de livraison d'un produit
 */
export default function DeliveryInfo({
  product,
  className = '',
  showIcon = true,
  compact = false
}: DeliveryInfoProps) {
  const { selectedZone, getDeliveryInfo, getDeliveryInfoWithChildren, setSelectedZone } = useDelivery();
  const { translate } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [deliveryInfo, setDeliveryInfo] = useState<DeliveryAvailabilityData | null>(null);
  const [deliveryInfoWithChildren, setDeliveryInfoWithChildren] = useState<DeliveryAvailabilityWithChildrenData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showChildZones, setShowChildZones] = useState(false);

  // Fonction pour récupérer les informations de livraison
  const fetchDeliveryInfo = async () => {
    if (!selectedZone || !product.id) {
      setDeliveryInfo(null);
      setDeliveryInfoWithChildren(null);
      return;
    }

    // Vérifier si nous avons déjà les informations en mémoire (dans l'état du composant)
    // Si oui, ne pas refaire l'appel API
    if (deliveryInfo && deliveryInfoWithChildren) {
      console.log('Using existing delivery info from state');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setShowChildZones(false);

      // Récupérer les informations de base
      const info = await getDeliveryInfo(product);
      console.log('Delivery info received:', info);
      setDeliveryInfo(info);

      // Récupérer les informations avec les zones enfants
      const infoWithChildren = await getDeliveryInfoWithChildren(product);
      console.log('Delivery info with children received:', infoWithChildren);
      setDeliveryInfoWithChildren(infoWithChildren);
    } catch (err) {
      console.error('Erreur lors de la récupération des informations de livraison:', err);
      setError(translate('Impossible de vérifier la disponibilité de livraison'));
      setDeliveryInfo(null);
      setDeliveryInfoWithChildren(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Déclencher la récupération des informations de livraison lorsque la zone change
  useEffect(() => {
    // Réinitialiser les états
    setDeliveryInfo(null);
    setDeliveryInfoWithChildren(null);
    setShowChildZones(false);
  }, [selectedZone?.id]);

  if (!selectedZone) {
    return (
      <div className={`flex items-center text-muted-foreground ${className}`}>
        {showIcon && <Truck className="h-4 w-4 mr-2" />}
        <span className="text-sm">{translate('Sélectionnez une zone de livraison')}</span>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`flex items-center ${className}`}>
        {showIcon && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
        <span className="text-sm">{translate('Vérification de la livraison...')}</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center text-destructive ${className}`}>
        {showIcon && <AlertCircle className="h-4 w-4 mr-2" />}
        <span className="text-sm">{error}</span>
      </div>
    );
  }

  // Si les informations de livraison n'ont pas encore été chargées, afficher un bouton pour les vérifier
  if (!deliveryInfo) {
    return (
      <div className={`flex flex-col items-center gap-2 ${className}`}>
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={fetchDeliveryInfo}
        >
          {showIcon && <Truck className="h-4 w-4 mr-2" />}
          {translate('Vérifier la disponibilité de livraison')}
        </Button>
      </div>
    );
  }

  // Si le produit n'est pas disponible dans la zone actuelle
  if (!deliveryInfo.available) {
    // Vérifier si nous avons des informations sur les zones enfants
    if (deliveryInfoWithChildren && deliveryInfoWithChildren.has_children) {
      // S'il y a des zones enfants disponibles
      if (deliveryInfoWithChildren.available_children.length > 0) {
        return (
          <div className={`space-y-2 ${className}`}>
            <div className="flex items-center text-amber-600">
              {showIcon && <AlertCircle className="h-4 w-4 mr-2" />}
              <span className="text-sm">{translate('Non livrable directement dans cette zone')}</span>
            </div>

            {showChildZones ? (
              <div className="pl-6 space-y-2">
                <div className="text-sm font-medium">{translate('Zones disponibles')}:</div>
                <div className="space-y-1">
                  {deliveryInfoWithChildren.available_children.map((zone) => (
                    <Button
                      key={zone.id}
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-left"
                      onClick={() => {
                        // Sélectionner cette zone enfant
                        setSelectedZone(zone);
                      }}
                    >
                      <ChevronRight className="h-3 w-3 mr-1" />
                      {zone.nom}
                    </Button>
                  ))}
                </div>
              </div>
            ) : (
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => setShowChildZones(true)}
              >
                {translate('Voir les zones disponibles')} ({deliveryInfoWithChildren.available_children.length})
              </Button>
            )}
          </div>
        );
      }
    }

    // Si pas de zones enfants ou pas d'informations sur les zones enfants
    return (
      <div className={`flex items-center text-destructive ${className}`}>
        {showIcon && <XCircle className="h-4 w-4 mr-2" />}
        <span className="text-sm">{translate('Non livrable dans cette zone')}</span>
      </div>
    );
  }

  if (compact) {
    return (
      <div className={`flex items-center ${className}`}>
        {showIcon && <CheckCircle className="h-4 w-4 mr-2 text-green-600" />}
        <span className="text-sm">
          {translate('Livraison disponible')}
        </span>
      </div>
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center">
        {showIcon && <CheckCircle className="h-4 w-4 mr-2 text-green-600" />}
        <span className="text-sm font-medium">
          {translate('Livrable à')} {selectedZone.nom}
        </span>
      </div>

      {deliveryInfo.livraison && (
        <div className="pl-6 space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">{translate('Frais de livraison')}:</span>
            <Badge variant="outline">
              {formatPrice(deliveryInfo.livraison.frais_livraison_specifique || deliveryInfo.livraison.frais_livraison)}
            </Badge>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">{translate('Délai estimé')}:</span>
            <Badge variant="outline">
              {deliveryInfo.livraison.delai_livraison_min === deliveryInfo.livraison.delai_livraison_max
                ? `${deliveryInfo.livraison.delai_livraison_min} ${translate('jours')}`
                : `${deliveryInfo.livraison.delai_livraison_min}-${deliveryInfo.livraison.delai_livraison_max} ${translate('jours')}`}
            </Badge>
          </div>
        </div>
      )}
    </div>
  );
}
