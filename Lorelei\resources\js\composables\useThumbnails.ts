import { computed, type ComputedRef } from 'vue';
import { 
  ThumbnailSize, 
  SingleThumbnailUrls, 
  MultipleThumbnailUrls, 
  ThumbnailUtils,
  type UseThumbnailsReturn
} from '@/types/thumbnails';

/**
 * Composable Vue pour gérer les miniatures
 */
export function useThumbnails(
  thumbnailUrls: SingleThumbnailUrls | MultipleThumbnailUrls,
  originalUrls: string | string[]
): UseThumbnailsReturn & {
  thumbnailUrl: ComputedRef<(size: ThumbnailSize, index?: number) => string>;
  hasThumbnailsComputed: ComputedRef<boolean>;
  srcSet: ComputedRef<(index?: number) => string>;
} {
  /**
   * Obtient l'URL de la miniature ou l'image originale
   */
  const thumbnailUrl = computed(() => {
    return (size: ThumbnailSize, index: number = 0): string => {
      return ThumbnailUtils.getThumbnailOrOriginal(thumbnailUrls, size, originalUrls, index);
    };
  });

  /**
   * Vérifie si des miniatures sont disponibles
   */
  const hasThumbnailsComputed = computed(() => {
    return ThumbnailUtils.hasThumbnails(thumbnailUrls);
  });

  /**
   * Génère un srcset pour les images responsives
   */
  const srcSet = computed(() => {
    return (index: number = 0): string => {
      return ThumbnailUtils.generateSrcSet(thumbnailUrls, index);
    };
  });

  /**
   * Obtient la miniature avec fallback
   */
  const getThumbnail = (size: ThumbnailSize, index: number = 0): string => {
    return ThumbnailUtils.getThumbnailOrOriginal(thumbnailUrls, size, originalUrls, index);
  };

  /**
   * Vérifie si des miniatures sont disponibles
   */
  const hasThumbnails = ThumbnailUtils.hasThumbnails(thumbnailUrls);

  /**
   * Génère un srcset
   */
  const generateSrcSet = (index: number = 0): string => {
    return ThumbnailUtils.generateSrcSet(thumbnailUrls, index);
  };

  /**
   * Obtient la meilleure taille disponible
   */
  const getBestSize = (preferredSize: ThumbnailSize = 'medium'): ThumbnailSize | null => {
    return ThumbnailUtils.getBestAvailableSize(thumbnailUrls, preferredSize);
  };

  return {
    // Fonctions réactives
    thumbnailUrl,
    hasThumbnailsComputed,
    srcSet,
    
    // Fonctions non-réactives (compatibilité)
    getThumbnail,
    hasThumbnails,
    generateSrcSet,
    getBestSize
  };
}

/**
 * Composable spécialisé pour les produits
 */
export function useProductThumbnails(
  thumbnailUrls: MultipleThumbnailUrls,
  imageUrls: string[]
) {
  const thumbnails = useThumbnails(thumbnailUrls, imageUrls);

  /**
   * Obtient la miniature de l'image principale
   */
  const getMainThumbnail = (size: ThumbnailSize = 'medium'): string => {
    return thumbnails.getThumbnail(size, 0);
  };

  /**
   * Obtient toutes les miniatures d'une taille donnée
   */
  const getAllThumbnails = (size: ThumbnailSize): string[] => {
    const urls = thumbnailUrls[size];
    if (urls && urls.length > 0) {
      return urls;
    }
    // Fallback vers les images originales
    return imageUrls;
  };

  /**
   * Obtient les miniatures pour la galerie (toutes sauf la première)
   */
  const getGalleryThumbnails = (size: ThumbnailSize = 'medium'): string[] => {
    const allThumbnails = getAllThumbnails(size);
    return allThumbnails.slice(1);
  };

  return {
    ...thumbnails,
    getMainThumbnail,
    getAllThumbnails,
    getGalleryThumbnails
  };
}

/**
 * Composable spécialisé pour les catégories
 */
export function useCategoryThumbnails(
  thumbnailUrls: SingleThumbnailUrls,
  imageUrl: string
) {
  const thumbnails = useThumbnails(thumbnailUrls, imageUrl);

  /**
   * Obtient la miniature de la catégorie avec taille par défaut
   */
  const getCategoryThumbnail = (size: ThumbnailSize = 'medium'): string => {
    return thumbnails.getThumbnail(size);
  };

  /**
   * Obtient la miniature pour les cartes de catégorie
   */
  const getCardThumbnail = (): string => {
    return getCategoryThumbnail('small');
  };

  /**
   * Obtient la miniature pour l'en-tête de catégorie
   */
  const getHeaderThumbnail = (): string => {
    return getCategoryThumbnail('large');
  };

  return {
    ...thumbnails,
    getCategoryThumbnail,
    getCardThumbnail,
    getHeaderThumbnail
  };
}

/**
 * Composable spécialisé pour les bannières
 */
export function useBannerThumbnails(
  thumbnailUrls: SingleThumbnailUrls,
  imageUrl: string
) {
  const thumbnails = useThumbnails(thumbnailUrls, imageUrl);

  /**
   * Obtient la miniature de la bannière selon la position
   */
  const getBannerThumbnail = (position: string = 'hero'): string => {
    // Adapter la taille selon la position
    switch (position) {
      case 'hero':
      case 'main':
        return thumbnails.getThumbnail('large');
      case 'sidebar':
      case 'footer':
        return thumbnails.getThumbnail('medium');
      case 'small':
      case 'widget':
        return thumbnails.getThumbnail('small');
      default:
        return thumbnails.getThumbnail('medium');
    }
  };

  return {
    ...thumbnails,
    getBannerThumbnail
  };
}

/**
 * Composable spécialisé pour les reviews
 */
export function useReviewThumbnails(
  thumbnailUrls: MultipleThumbnailUrls,
  imageUrls: string[]
) {
  const thumbnails = useThumbnails(thumbnailUrls, imageUrls);

  /**
   * Obtient les miniatures pour l'affichage dans les reviews
   */
  const getReviewThumbnails = (size: ThumbnailSize = 'small'): string[] => {
    const urls = thumbnailUrls[size];
    if (urls && urls.length > 0) {
      return urls;
    }
    // Fallback vers les images originales
    return imageUrls;
  };

  /**
   * Obtient une miniature spécifique pour les reviews
   */
  const getReviewThumbnail = (index: number, size: ThumbnailSize = 'small'): string => {
    return thumbnails.getThumbnail(size, index);
  };

  return {
    ...thumbnails,
    getReviewThumbnails,
    getReviewThumbnail
  };
}
