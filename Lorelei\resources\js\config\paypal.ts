/**
 * Configuration PayPal pour l'application
 */

// Configuration par défaut pour PayPal
export const PAYPAL_CONFIG = {
    // Client ID PayPal (à remplacer par une variable d'environnement en production)
    clientId: "AbkVIJ620bGTT-jchProcLH3SQXZ99X-uGc7aXEi_j8M9nMpaTOdfu-ROQs6KQL3Hlv9nIX-nBr0HW31", // à remplacer par une variable d'environnement en production

    // Devise par défaut
    currency: "EUR",

    // Intent (capture ou authorize)
    intent: "capture",

    // Composants à charger
    components: "buttons",

    // Activer certains modes de paiement
    "enable-funding": "paylater,venmo,card",

    // Désactiver certains modes de paiement
    "disable-funding": "credit",

    // Namespace pour éviter les conflits
    "data-namespace": "paypal_sdk",

    // Type de page
    "data-page-type": "checkout",

    // Debug mode (à activer uniquement en développement)
    "debug": import.meta.env.DEV ? true : false,
};

/**
 * Fonction pour obtenir la configuration PayPal
 * @param overrides - Paramètres à remplacer dans la configuration par défaut
 * @returns Configuration PayPal complète
 */
export const getPayPalConfig = (overrides = {}) => {
    return {
        ...PAYPAL_CONFIG,
        ...overrides,
    };
};

export default PAYPAL_CONFIG;
