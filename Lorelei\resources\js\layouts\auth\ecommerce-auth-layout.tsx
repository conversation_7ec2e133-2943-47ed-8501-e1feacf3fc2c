import { ReactNode } from 'react';
import { Link } from '@inertiajs/react';
import EcommerceHeader from '@/components/ecommerce/EcommerceHeader';
import EcommerceFooter from '@/components/ecommerce/EcommerceFooter';

interface EcommerceAuthLayoutProps {
  children: ReactNode;
  title: string;
  description: string;
}

/**
 * Layout pour les pages d'authentification dans le style e-commerce
 */
export default function EcommerceAuthLayout({ children, title, description }: EcommerceAuthLayoutProps) {
  return (
    <div className="flex min-h-screen flex-col">
      <EcommerceHeader />
      <div className="h-[110px] md:h-[120px]"></div>
      <main className="flex-1 flex items-center justify-center py-12">
        <div className="w-full max-w-md px-4">
          <div className="mb-8 text-center">
            <h1 className="text-2xl font-bold mb-2">{title}</h1>
            <p className="text-muted-foreground">{description}</p>
          </div>
          <div className="bg-card border rounded-lg shadow-sm p-6">
            {children}
          </div>
        </div>
      </main>
      <EcommerceFooter />
    </div>
  );
}
