/**
 * <PERSON><PERSON><PERSON><PERSON> représentant une bannière
 */
export class Banner {
  /**
   * URLs des miniatures de l'image de la bannière par taille
   */
  public thumbnailUrls: {
    small: string | null;
    medium: string | null;
    large: string | null;
  } = {
    small: null,
    medium: null,
    large: null
  };
  /**
   * Constructeur du modèle Banner
   *
   * @param id - Identifiant unique de la bannière
   * @param position - Position de la bannière sur le site
   * @param title - Titre de la bannière
   * @param description - Description de la bannière
   * @param buttonText - Texte du bouton de la bannière
   * @param type - Type de bannière (primary, secondary, promotional)
   * @param imageUrl - URL de l'image de la bannière (optionnel)
   * @param targetUrl - URL de destination lors du clic sur la bannière
   * @param isActive - Indique si la bannière est active
   * @param thumbnailUrls - URLs des miniatures par taille
   */
  constructor(
    public id: string,
    public position: string,
    public title: string | null,
    public description: string | null,
    public buttonText: string | null,
    public type: string | null,
    public imageUrl: string | null,
    public targetUrl: string | null,
    public isActive: boolean = true,
    thumbnailUrls: { small: string | null; medium: string | null; large: string | null } = { small: null, medium: null, large: null }
  ) {
    // Initialiser les miniatures
    this.thumbnailUrls = thumbnailUrls;
  }

  /**
   * Retourne l'URL complète de l'image
   */
  get fullImageUrl(): string | null {
    return this.imageUrl;
  }
}
