/**
 * Classe représentant un avis dans l'application e-commerce
 *
 * Cette classe encapsule toutes les propriétés et méthodes liées à un avis
 */
export class Review {
  /**
   * URLs des miniatures des images de l'avis par taille
   */
  public thumbnailUrls: {
    small: string[];
    medium: string[];
    large: string[];
  } = {
    small: [],
    medium: [],
    large: []
  };
  /**
   * Crée une nouvelle instance d'avis
   *
   * @param id - Identifiant unique de l'avis
   * @param productId - Identifiant du produit associé
   * @param authorName - Nom de l'auteur de l'avis
   * @param rating - Note attribuée (1-5)
   * @param comment - Commentaire textuel
   * @param createdAt - Date de création de l'avis
   * @param likes - Nombre de likes
   * @param dislikes - Nombre de dislikes
   * @param imageUrls - URLs des images jointes
   * @param userName - Nom de l'utilisateur (si connecté)
   * @param thumbnailUrls - URLs des miniatures par taille
   */
  constructor(
    public id: string,
    public productId: string,
    public authorName: string,
    public rating: number,
    public comment: string,
    public createdAt: string,
    public likes: number = 0,
    public dislikes: number = 0,
    public imageUrls: string[] = [],
    public userName: string | null = null,
    thumbnailUrls: { small: string[]; medium: string[]; large: string[] } = { small: [], medium: [], large: [] }
  ) {
    // Initialiser les miniatures
    this.thumbnailUrls = thumbnailUrls;
  }

  /**
   * Obtient la date formatée pour l'affichage
   *
   * @returns La date formatée (ex: "Il y a 2 jours")
   */
  getFormattedDate(): string {
    // Utiliser la date relative fournie par l'API
    const date = new Date(this.createdAt);

    // Calculer la différence en millisecondes
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();

    // Convertir en minutes, heures, jours
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // Formater la date relative
    if (diffMinutes < 1) {
      return "À l'instant";
    } else if (diffMinutes < 60) {
      return `Il y a ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
    } else if (diffHours < 24) {
      return `Il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    } else if (diffDays < 30) {
      return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    } else {
      // Format de date standard pour les dates plus anciennes
      return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  }

  /**
   * Obtient la date exacte pour l'infobulle
   *
   * @returns La date exacte formatée
   */
  getExactDate(): string {
    const date = new Date(this.createdAt);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Vérifie si l'avis a des images
   *
   * @returns true si l'avis a des images, false sinon
   */
  hasImages(): boolean {
    return this.imageUrls.length > 0;
  }

  /**
   * Obtient le nom d'affichage de l'auteur
   *
   * @returns Le nom d'utilisateur s'il existe, sinon le nom de l'auteur
   */
  getDisplayName(): string {
    return this.userName || this.authorName;
  }
}
