import { Head } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import { Link } from '@inertiajs/react';
import { Category } from '@/models/Category';
import { CategoryService } from '@/services/CategoryService';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useTranslation } from '@/hooks/use-translation';

export default function Categories() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const categoryService = new CategoryService();
  const { tDefault } = useTranslation();

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true);
        const mainCategories = await categoryService.getMainCategories();
        setCategories(mainCategories);
      } catch (error) {
        console.error('Erreur lors de la récupération des catégories:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return (
    <EcommerceLayout>
      <Head title={tDefault('pages.categories.title', 'Toutes les catégories - Lorrelei')} />

      <div className="container mx-auto px-4 py-8">
        <h1 className="mb-8 text-3xl font-bold">{tDefault('pages.categories.all_categories', 'Toutes les catégories')}</h1>

        {isLoading ? (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {Array.from({ length: 8 }).map((_, index) => (
              <Card key={index} className="overflow-hidden">
                <Skeleton className="h-40 w-full" />
                <CardContent className="p-4">
                  <Skeleton className="mb-2 h-6 w-3/4" />
                  <Skeleton className="h-4 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {categories.map((category) => (
              <Link key={category.id} href={`/categories/${category.slug}`}>
                <Card className="overflow-hidden transition-all hover:shadow-lg">
                  {category.imageUrl ? (
                    <div
                      className="h-40 w-full bg-cover bg-center"
                      style={{ backgroundImage: `url(${category.imageUrl})` }}
                    />
                  ) : (
                    <div className="h-40 w-full flex items-center justify-center bg-gradient-to-r from-primary/20 to-primary/40">
                      <span className="text-6xl font-bold text-primary">
                        {category.getTranslatedName().charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  <CardContent className="p-4">
                    <h3 className="mb-2 text-lg font-semibold">{category.getTranslatedName()}</h3>
                    <p className="text-sm text-muted-foreground line-clamp-2">{category.getTranslatedDescription()}</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        )}
      </div>
    </EcommerceLayout>
  );
}
