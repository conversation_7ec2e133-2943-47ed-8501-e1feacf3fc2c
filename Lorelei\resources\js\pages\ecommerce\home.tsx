import { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import Carousel from '@/components/ecommerce/Carousel';
import ListeCategories from '@/components/ecommerce/ListeCategories';
import BannerComponent from '@/components/ecommerce/Banner';
import ProductListInfinite from '@/components/ecommerce/ProductListInfinite';
import { ProductService } from '@/services/ProductService';
import { CategoryService } from '@/services/CategoryService';
import { BannerService } from '@/services/BannerService';
import { Product } from '@/models/Product';
import { Category } from '@/models/Category';
import { Banner } from '@/models/Banner';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { useTranslation } from '@/hooks/use-translation';

/**
 * Page d'accueil de l'application e-commerce
 */
export default function Home() {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [carouselBanners, setCarouselBanners] = useState<Banner[]>([]);
  const [promotionalBanners, setPromotionalBanners] = useState<Banner[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { translate } = useTranslation();

  // Récupération des données
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const productService = new ProductService();
        const categoryService = new CategoryService();
        const bannerService = new BannerService();

        const [products, cats, mainBanners, promoBanners] = await Promise.all([
          productService.getFeaturedProducts(8),
          categoryService.getMainCategories(),
          bannerService.getBannersByPosition('carousel'),
          bannerService.getBannersByPosition('promotional'),
        ]);
        setFeaturedProducts(products);
        setCategories(cats);
        setCarouselBanners(mainBanners);
        setPromotionalBanners(promoBanners);
      } catch (error) {
        console.error('Erreur lors de la récupération des données:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <EcommerceLayout>
      <Head title={translate('pages.home.title')} />

      {/* Carousel principal */}
      <section className="container mx-auto px-4 py-6">
        {isLoading ? (
          <div className="aspect-[21/9] w-full animate-pulse rounded-xl bg-muted"></div>
        ) : carouselBanners.length > 0 ? (
          <Carousel>
            {carouselBanners.map((banner) => (
              <div key={banner.id} className="relative aspect-[21/9] w-full">
                <BannerComponent
                  banner={banner}
                  className="h-full"
                />
              </div>
            ))}
          </Carousel>
        ) : null /* Ne rien afficher s'il n'y a pas de bannières */}
      </section>

      {/* Catégories principales */}
      <section className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <h2 className=" text-xl md:text-2xl font-bold ">{translate('pages.home.popular_categories')}</h2>
          <Link href={route('categories')} className="flex items-center text-xs md:text-sm font-medium text-primary hover:underline">
            {translate('pages.home.all_categories')}
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="animate-pulse rounded-lg border">
                <div className="aspect-square bg-muted"></div>
                <div className="p-3">
                  <div className="h-4 w-3/4 rounded bg-muted"></div>
                </div>
              </div>
            ))}
          </div>
        ) : categories.length > 0 ? (
          <ListeCategories categories={categories.slice(0, 10)} />
        ): null /* Ne rien afficher s'il n'y a pas de catégories */}
      </section>

      {/* Produits en vedette */}
      <section className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-2xl font-bold">{translate('pages.home.featured_products')}</h2>
          <Link href={route('products')} className="flex items-center text-sm font-medium text-primary hover:underline">
            {translate('pages.home.all_products')}
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-2 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="animate-pulse rounded-lg border">
                <div className="aspect-square bg-muted"></div>
                <div className="p-4 space-y-3">
                  <div className="h-4 w-1/4 rounded bg-muted"></div>
                  <div className="h-4 w-3/4 rounded bg-muted"></div>
                  <div className="h-4 w-1/2 rounded bg-muted"></div>
                  <div className="h-8 rounded bg-muted"></div>
                </div>
              </div>
            ))}
          </div>
        ) : featuredProducts.length > 0 ? (
          <ProductListInfinite
            initialProducts={featuredProducts}
            itemsPerPage={8}
            emptyMessage={translate('common.no_products_found')}
            showPagination={false} // Pas de pagination sur la page d'accueil pour les produits en vedette
          />
        ): null /* Ne rien afficher s'il n'y a pas de produits en vedette */}
      </section>

      {/* Bannières promotionnelles */}
      {isLoading ? (
        <section className="container mx-auto px-4 py-8">
          <div className="grid gap-6 md:grid-cols-2">
            <div className="aspect-[20/9] w-full animate-pulse rounded-xl bg-muted"></div>
            <div className="aspect-[20/9] w-full animate-pulse rounded-xl bg-muted"></div>
          </div>
        </section>
      ) : promotionalBanners.length > 0 ? (
        <section className="container mx-auto px-4 py-8">
          <div className="grid gap-6 md:grid-cols-2">
            {promotionalBanners.map((banner) => (
              <div key={banner.id}>
                <BannerComponent banner={banner} />
              </div>
            ))}
          </div>
        </section>
      ) : null /* Ne rien afficher s'il n'y a pas de bannières */}

      {/* Avantages */}
      <section className="bg-muted/30 py-12">
        <div className="container mx-auto px-4">
          <h2 className="mb-8 text-center text-2xl font-bold">{translate('pages.home.why_choose_us')}</h2>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="rounded-lg bg-background p-6 text-center shadow-sm">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="mb-2 text-lg font-semibold">{translate('pages.home.quality_guaranteed')}</h3>
              <p className="text-sm text-muted-foreground">
                {translate('pages.home.quality_guaranteed_description')}
              </p>
            </div>
            <div className="rounded-lg bg-background p-6 text-center shadow-sm">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="mb-2 text-lg font-semibold">{translate('pages.home.fast_delivery')}</h3>
              <p className="text-sm text-muted-foreground">
                {translate('pages.home.fast_delivery_description')}
              </p>
            </div>
            <div className="rounded-lg bg-background p-6 text-center shadow-sm">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </div>
              <h3 className="mb-2 text-lg font-semibold">{translate('pages.home.secure_payment')}</h3>
              <p className="text-sm text-muted-foreground">
                {translate('pages.home.secure_payment_description')}
              </p>
            </div>
          </div>
        </div>
      </section>
    </EcommerceLayout>
  );
}
