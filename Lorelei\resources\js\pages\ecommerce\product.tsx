import { useState, useEffect, useMemo, useCallback } from 'react';
import { Head } from '@inertiajs/react';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import { ProductService } from '@/services/ProductService';
import { CategoryService } from '@/services/CategoryService';
import { Product } from '@/models/Product';
import { Category } from '@/models/Category';
import { ProductVariant, ProductAttribute } from '@/models/ProductVariant';
import { DeliveryInfo as DeliveryInfoType } from '@/models/CartItem';
import { useCart } from '@/contexts/CartContext';
import { useDelivery } from '@/contexts/DeliveryContext';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Link } from '@inertiajs/react';
import { Minus, Plus, Share2, ShoppingCart, Star, Truck, Loader2, Ruler, RefreshCw, ChevronLeft, ChevronRight, ZoomIn, MapPin } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from '@/hooks/use-toast';
import WishlistButton from '@/components/ecommerce/WishlistButton';
import ReviewsList from '@/components/ecommerce/ReviewsList';
import BreadcrumbTrail from '@/components/ecommerce/BreadcrumbTrail';
import ProductVariantSelector from '@/components/ecommerce/ProductVariantSelector';
import SizeGuideModal from '@/components/ecommerce/SizeGuideModal';
import ProductListInfinite from '@/components/ecommerce/ProductListInfinite';
import ZoneLivraisonSelector from '@/components/ecommerce/ZoneLivraisonSelector';
import DeliveryInfo from '@/components/ecommerce/DeliveryInfo';
import ProductAvailableZones from '@/components/ecommerce/ProductAvailableZones';
import { useTranslation } from '@/hooks/use-translation';

/**
 * Props pour la page de produit
 */
interface ProductPageProps {
  productSlug: string;
}

/**
 * Page affichant les détails d'un produit
 *
 * @param productSlug - Le slug du produit à afficher
 */
export default function ProductPage({ productSlug }: ProductPageProps) {
  const [product, setProduct] = useState<Product | null>(null);
  const [category, setCategory] = useState<Category | null>(null);
  const [similarProducts, setSimilarProducts] = useState<Product[]>([]);
  const [selectedImage, setSelectedImage] = useState<string>('');
  const [quantity, setQuantity] = useState<number>(1);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [isSizeGuideOpen, setIsSizeGuideOpen] = useState(false);
  const [selectedColorVariant, setSelectedColorVariant] = useState<ProductVariant | null>(null);
  const { addItem } = useCart();
  const { selectedZone, getDeliveryInfo } = useDelivery();
  const { translate } = useTranslation();

  // État pour stocker les attributs sélectionnés par l'utilisateur
  const [selectedAttributes, setSelectedAttributes] = useState<{
    color: string | null;
    size: string | null;
    material: string | null;
  }>({
    color: null,
    size: null,
    material: null
  });

  // Utiliser useMemo pour éviter de recréer les services à chaque rendu
  const productService = useMemo(() => new ProductService(), []);
  const categoryService = useMemo(() => new CategoryService(), []);

  // Récupération des données
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const productData = await productService.getProductBySlug(productSlug);
        if (productData) {
            setProduct(productData);
            setSelectedImage(productData.imageUrl);

            // Récupérer la catégorie
            const categoryData = await categoryService.getCategoryById(productData.category);
            setCategory(categoryData);

            // Récupérer des produits similaires pour l'initialisation
            const similarProductsResult = await productService.getProductsByCategory(
              productData.category,
              4, // Nombre initial de produits similaires à afficher
              1, // Page 1
              productData.id // Exclure le produit actuel
            );
            setSimilarProducts(similarProductsResult);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des données:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [productSlug, productService, categoryService]);

  // État pour suivre l'index de l'image actuelle
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);

  // État pour suivre la direction de l'animation (1 pour avant, -1 pour arrière)
  const [direction, setDirection] = useState<number>(1);

  // État pour le mode zoom
  const [isZoomed, setIsZoomed] = useState<boolean>(false);

  // État pour le chargement des images
  const [imageLoading, setImageLoading] = useState<boolean>(true);

  // Fonction pour obtenir toutes les images disponibles en fonction de la variante sélectionnée
  const getAllImages = useMemo(() => {
    if (!product) return [];

    if (selectedVariant && selectedVariant.imageUrls.length > 0) {
      return selectedVariant.imageUrls;
    } else if (selectedColorVariant && selectedColorVariant.imageUrls.length > 0) {
      return selectedColorVariant.imageUrls;
    } else {
      return [...product.mainImageUrls, ...product.additionalImageUrls];
    }
  }, [product, selectedVariant, selectedColorVariant]);

  // Initialiser l'image principale avec celle du produit
  useEffect(() => {
    if (product) {
      // Toujours commencer par afficher les images du produit principal
      if (product.mainImageUrls.length > 0) {
        setSelectedImage(product.mainImageUrls[0]);
        setCurrentImageIndex(0);
      } else if (product.imageUrl) {
        setSelectedImage(product.imageUrl);
        setCurrentImageIndex(0);
      }
    }
  }, [product]);

  // Fonction pour naviguer vers l'image précédente (avec useCallback pour éviter les problèmes de dépendances)
  const goToPreviousImage = useCallback(() => {
    if (getAllImages.length <= 1) return;

    // Définir la direction de l'animation vers la gauche (arrière)
    setDirection(-1);

    const newIndex = currentImageIndex === 0 ? getAllImages.length - 1 : currentImageIndex - 1;
    setSelectedImage(getAllImages[newIndex]);
    setCurrentImageIndex(newIndex);
  }, [getAllImages, currentImageIndex, setSelectedImage]);

  // Fonction pour naviguer vers l'image suivante (avec useCallback pour éviter les problèmes de dépendances)
  const goToNextImage = useCallback(() => {
    if (getAllImages.length <= 1) return;

    // Définir la direction de l'animation vers la droite (avant)
    setDirection(1);

    const newIndex = currentImageIndex === getAllImages.length - 1 ? 0 : currentImageIndex + 1;
    setSelectedImage(getAllImages[newIndex]);
    setCurrentImageIndex(newIndex);
  }, [getAllImages, currentImageIndex, setSelectedImage]);

  // Fonction pour basculer le mode zoom
  const toggleZoom = useCallback(() => {
    setIsZoomed(prev => !prev);
  }, []);

  // Mettre à jour l'index de l'image actuelle lorsque l'image sélectionnée change
  useEffect(() => {
    const index = getAllImages.findIndex(img => img === selectedImage);
    if (index !== -1) {
      setCurrentImageIndex(index);
    }

    // Réinitialiser l'état de chargement de l'image
    setImageLoading(true);
  }, [selectedImage, getAllImages]);

  // Ajouter la navigation au clavier pour les images
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ne pas intercepter les événements clavier si l'utilisateur est en train de taper dans un champ
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      if (e.key === 'ArrowLeft') {
        goToPreviousImage();
      } else if (e.key === 'ArrowRight') {
        goToNextImage();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [goToPreviousImage, goToNextImage]);

  // Mettre à jour selectedColorVariant lorsque selectedAttributes change
  useEffect(() => {
    if (product && selectedAttributes.color) {
      const colorVariant = product.variants.find(v =>
        v.attributes.some(a =>
          a.type === 'couleur' && 'nom' in a && a.nom === selectedAttributes.color
        )
      );
      setSelectedColorVariant(colorVariant || null);
    } else {
      setSelectedColorVariant(null);
    }
  }, [product, selectedAttributes.color]);

  /**
   * Augmente la quantité
   */
  const increaseQuantity = () => {
    setQuantity(prev => prev + 1);
  };

  /**
   * Diminue la quantité (minimum 1)
   */
  const decreaseQuantity = () => {
    setQuantity(prev => (prev > 1 ? prev - 1 : 1));
  };

  /**
   * Ajoute le produit au panier avec un effet de chargement et une notification
   */
  const handleAddToCart = () => {
    if (product) {
      // Vérifier si le produit ou ses variantes ont des attributs de taille
      const hasSizeVariants = product.variants.some(v =>
        v.attributes.some(a => a.type === 'taille')
      );
      const hasProductSizes = product.attributes.some(a => a.type === 'taille');
      const hasSizes = hasSizeVariants || hasProductSizes;

      console.log('selectedVariant', selectedVariant);
      console.log('selectedAttributes', selectedAttributes);

      // Si des tailles sont disponibles mais qu'aucune taille n'est sélectionnée
      if (hasSizes && !selectedAttributes.size) {
        // Afficher une notification d'erreur
        toast({
          title: translate('common.size_required'),
          description: translate('common.size_required_description'),
          variant: "destructive",
        });
        return;
      }

      setIsAddingToCart(true);

      // Simuler un délai pour montrer le loader (peut être supprimé en production)
      setTimeout(() => {
        // Si une variante est sélectionnée, ajouter la variante au panier
        if (selectedVariant) {
          // Ajouter la variante au panier
          // Créer un nouveau produit basé sur la variante
          // Construire une description personnalisée basée sur les attributs sélectionnés par l'utilisateur
          const attributeDescriptions = [];

          if (selectedAttributes.color) {
            attributeDescriptions.push(`${translate('common.color')}: ${selectedAttributes.color}`);
          }

          if (selectedAttributes.size) {
            attributeDescriptions.push(`${translate('common.size')}: ${selectedAttributes.size}`);
          }

          if (selectedAttributes.material) {
            attributeDescriptions.push(`${translate('common.material')}: ${selectedAttributes.material}`);
          }

          const variantDescription = attributeDescriptions.join(', ');

          // Créer un tableau d'attributs basé uniquement sur les sélections de l'utilisateur
          const combinedAttributes: ProductAttribute[] = [];

          // Ajouter les attributs sélectionnés par l'utilisateur
          if (selectedAttributes.color) {
            // Trouver l'attribut de couleur correspondant dans la variante
            const colorAttr = selectedVariant.attributes.find(
              a => a.type === 'couleur' && 'nom' in a && a.nom === selectedAttributes.color
            );

            // Si trouvé, l'ajouter aux attributs combinés
            if (colorAttr) {
              combinedAttributes.push(colorAttr);
            }
          }

          if (selectedAttributes.size) {
            // Trouver l'attribut de taille correspondant dans la variante
            const sizeAttr = selectedVariant.attributes.find(
              a => a.type === 'taille' && 'valeur' in a && a.valeur === selectedAttributes.size
            );

            // Si trouvé, l'ajouter aux attributs combinés
            if (sizeAttr) {
              combinedAttributes.push(sizeAttr);
            }
          }

          if (selectedAttributes.material) {
            // Trouver l'attribut de matière correspondant dans la variante
            const materialAttr = selectedVariant.attributes.find(
              a => a.type === 'matiere' && 'valeur' in a && a.valeur === selectedAttributes.material
            );

            // Si trouvé, l'ajouter aux attributs combinés
            if (materialAttr) {
              combinedAttributes.push(materialAttr);
            }
          }

          // Ajouter les autres attributs de la variante qui ne sont pas de type couleur, taille ou matière
          selectedVariant.attributes.forEach(attr => {
            if (attr.type !== 'couleur' && attr.type !== 'taille' && attr.type !== 'matiere') {
              // Vérifier si cet attribut existe déjà dans les attributs combinés
              const attrExists = combinedAttributes.some(
                varAttr => varAttr.type === attr.type &&
                          (('nom' in varAttr && 'nom' in attr && varAttr.nom === attr.nom) ||
                           ('valeur' in varAttr && 'valeur' in attr && varAttr.valeur === attr.valeur))
              );

              // Si l'attribut n'existe pas déjà, l'ajouter
              if (!attrExists) {
                combinedAttributes.push(attr);
              }
            }
          });

          // N'ajouter que les attributs du produit principal qui ne sont pas de type couleur, taille ou matière
          // et qui ne sont pas déjà présents dans les attributs combinés
          product.attributes.forEach(attr => {
            // Ne pas ajouter les attributs de type couleur, taille ou matière du produit principal
            // car ils sont déjà gérés par les sélections de l'utilisateur
            if (attr.type !== 'couleur' && attr.type !== 'taille' && attr.type !== 'matiere') {
              // Vérifier si cet attribut existe déjà dans les attributs combinés
              const attrExists = combinedAttributes.some(
                varAttr => varAttr.type === attr.type &&
                          (('nom' in varAttr && 'nom' in attr && varAttr.nom === attr.nom) ||
                           ('valeur' in varAttr && 'valeur' in attr && varAttr.valeur === attr.valeur))
              );

              // Si l'attribut n'existe pas déjà, l'ajouter
              if (!attrExists) {
                combinedAttributes.push(attr);
              }
            }
          });

          // Afficher les attributs combinés pour le débogage
          console.log('Attributs combinés avant création du produit variante:', combinedAttributes);

          const variantProduct = new Product(
            `${product.id}-${selectedVariant.id}`, // ID unique pour la variante
            `${product.name} - ${variantDescription}`, // Nom avec description de la variante
            product.slug,
            product.productCode,
            product.brand,
            product.description,
            selectedVariant.getTotalPrice(product.getCurrentPrice()), // Prix avec supplément
            product.currency,
            product.discountPrice,
            product.discountStartDate,
            product.discountEndDate,
            selectedVariant.imageUrls.length > 0 ? selectedVariant.imageUrls[0] : product.imageUrl,
            selectedVariant.imageUrls.length > 0 ? selectedVariant.imageUrls : product.imageUrls,
            product.category,
            selectedVariant.inStock, // Stock de la variante
            product.rating,
            product.reviews,
            product.seller,
            selectedVariant.imageUrls.length > 0 ? selectedVariant.imageUrls.slice(0, 2) : product.mainImageUrls,
            selectedVariant.imageUrls.length > 0 ? selectedVariant.imageUrls.slice(2) : product.additionalImageUrls,
            combinedAttributes, // Utiliser les attributs combinés au lieu de product.attributes
            [] // Pas besoin de transmettre les variantes
          );

          // Afficher le produit variante pour le débogage
          console.log('Produit variante créé:', variantProduct);
          console.log('Attributs du produit variante:', variantProduct.attributes);

          // Récupérer les informations de livraison si une zone est sélectionnée
          if (selectedZone) {
            console.log("Zone sélectionnée pour la variante:", selectedZone);
            // Récupérer les informations de livraison pour ce produit dans la zone sélectionnée
            getDeliveryInfo(variantProduct).then(deliveryInfo => {
              console.log("Infos de livraison pour la variante:", deliveryInfo);
              if (deliveryInfo && deliveryInfo.available && deliveryInfo.livraison) {
                // Créer un objet DeliveryInfoType avec les informations de livraison
                const frais_livraison = Number(deliveryInfo.livraison.frais_livraison);
                const frais_specifiques = deliveryInfo.livraison.frais_livraison_specifique !== undefined ?
                  Number(deliveryInfo.livraison.frais_livraison_specifique) : undefined;

                const cartDeliveryInfo: DeliveryInfoType = {
                  frais_livraison: frais_livraison,
                  delai_livraison_min: deliveryInfo.livraison.delai_livraison_min,
                  delai_livraison_max: deliveryInfo.livraison.delai_livraison_max,
                  frais_livraison_specifique: frais_specifiques,
                  zone_id: selectedZone.id,
                  zone_nom: selectedZone.nom
                };
                console.log("Ajout de la variante au panier avec frais:", cartDeliveryInfo.frais_livraison);
                console.log("Informations de livraison complètes:", cartDeliveryInfo);

                // Ajouter le produit variante au panier avec les informations de livraison
                addItem(variantProduct, quantity, cartDeliveryInfo);
              } else {
                console.log("La variante n'est pas livrable dans cette zone");
                // Si le produit n'est pas livrable dans cette zone, l'ajouter sans informations de livraison
                addItem(variantProduct, quantity);
              }
            }).catch(error => {
              console.error('Erreur lors de la récupération des informations de livraison:', error);
              // En cas d'erreur, ajouter le produit sans informations de livraison
              addItem(variantProduct, quantity);
            });
          } else {
            // Si aucune zone n'est sélectionnée, ajouter le produit sans informations de livraison
            addItem(variantProduct, quantity);
          }
        } else {
          // Pour le produit de base, faire la même chose
          if (selectedZone) {
            console.log("Zone sélectionnée pour le produit de base:", selectedZone);
            // Récupérer les informations de livraison pour ce produit dans la zone sélectionnée
            getDeliveryInfo(product).then(deliveryInfo => {
              console.log("Infos de livraison pour le produit de base:", deliveryInfo);
              if (deliveryInfo && deliveryInfo.available && deliveryInfo.livraison) {
                // Créer un objet DeliveryInfoType avec les informations de livraison
                const frais_livraison = Number(deliveryInfo.livraison.frais_livraison);
                const frais_specifiques = deliveryInfo.livraison.frais_livraison_specifique !== undefined ?
                  Number(deliveryInfo.livraison.frais_livraison_specifique) : undefined;

                const cartDeliveryInfo: DeliveryInfoType = {
                  frais_livraison: frais_livraison,
                  delai_livraison_min: deliveryInfo.livraison.delai_livraison_min,
                  delai_livraison_max: deliveryInfo.livraison.delai_livraison_max,
                  frais_livraison_specifique: frais_specifiques,
                  zone_id: selectedZone.id,
                  zone_nom: selectedZone.nom
                };
                console.log("Ajout du produit de base au panier avec frais:", cartDeliveryInfo.frais_livraison);
                console.log("Informations de livraison complètes:", cartDeliveryInfo);
                // Ajouter le produit au panier avec les informations de livraison
                addItem(product, quantity, cartDeliveryInfo);
              } else {
                console.log("Le produit de base n'est pas livrable dans cette zone");
                // Si le produit n'est pas livrable dans cette zone, l'ajouter sans informations de livraison
                addItem(product, quantity);
              }
            }).catch(error => {
              console.error('Erreur lors de la récupération des informations de livraison:', error);
              // En cas d'erreur, ajouter le produit sans informations de livraison
              addItem(product, quantity);
            });
          } else {
            // Si aucune zone n'est sélectionnée, ajouter le produit sans informations de livraison
            addItem(product, quantity);
          }
        }

        setIsAddingToCart(false);

        // Afficher une notification toast
        toast({
          title: translate('common.added_to_cart'),
          description: translate('common.added_to_cart_description', {
            quantity: quantity,
            product: product.name,
            plural: quantity > 1 ? translate('common.added_to_cart_plural_many') : translate('common.added_to_cart_plural_one')
          }),
          variant: "success",
        });
      }, 600);
    }
  };

  if (isLoading) {
    return (
      <EcommerceLayout>
        <Head title={translate('product.loading_title')} />
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 w-1/3 rounded bg-muted"></div>
            <div className="mt-8 grid gap-8 md:grid-cols-2">
              <div className="space-y-4">
                <div className="aspect-square rounded bg-muted"></div>
                <div className="flex gap-2">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div key={i} className="h-20 w-20 rounded bg-muted"></div>
                  ))}
                </div>
              </div>
              <div className="space-y-4">
                <div className="h-8 w-3/4 rounded bg-muted"></div>
                <div className="h-4 w-1/4 rounded bg-muted"></div>
                <div className="h-4 w-1/2 rounded bg-muted"></div>
                <div className="h-6 w-1/3 rounded bg-muted"></div>
                <div className="h-10 w-full rounded bg-muted"></div>
                <div className="h-10 w-full rounded bg-muted"></div>
              </div>
            </div>
          </div>
        </div>
      </EcommerceLayout>
    );
  }

  if (!product) {
    return (
      <EcommerceLayout>
        <Head title={translate('pages.product.not_found_title')} />
        <div className="container mx-auto px-4 py-8">
          <div className="rounded-lg border border-dashed p-8 text-center">
            <h1 className="mb-2 text-2xl font-bold">{translate('pages.product.not_found')}</h1>
            <p className="mb-6 text-muted-foreground">
              {translate('pages.product.not_found_description')}
            </p>
            <Button asChild>
              <Link href={route('home')}>{translate('common.go_home')}</Link>
            </Button>
          </div>
        </div>
      </EcommerceLayout>
    );
  }

  return(
    <EcommerceLayout>
      <Head title={translate('pages.product.title', { product: product.name })} />

      <div className="container mx-auto px-4 py-8">
        {/* Fil d'Ariane */}
        <BreadcrumbTrail category={category || undefined} product={product} className="mb-6" />

        {/* Détails du produit */}
        <div className="grid grid-cols-1 gap-4 sm:gap-6 md:gap-8 md:grid-cols-2">
          {/* Galerie d'images */}
          <div className="space-y-4">
            <div className="relative aspect-square overflow-hidden rounded-lg border">
              {/* Conteneur d'animation pour l'image */}
              <AnimatePresence mode="wait" initial={false} custom={direction}>
                <motion.div
                  key={selectedImage}
                  custom={direction}
                  initial={{
                    opacity: 0,
                    x: direction * 20
                  }}
                  animate={{
                    opacity: 1,
                    x: 0,
                    scale: isZoomed ? 1.5 : 1,
                    transition: { duration: 0.3 }
                  }}
                  exit={{
                    opacity: 0,
                    x: direction * -20,
                    transition: { duration: 0.3 }
                  }}
                  className="h-full w-full"
                  style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}
                  drag="x"
                  dragConstraints={{ left: 0, right: 0 }}
                  dragElastic={0.2}
                  onDragEnd={(e, { offset, velocity }) => {
                    const swipe = Math.abs(offset.x) > 50 || Math.abs(velocity.x) > 500;

                    if (swipe) {
                      if (offset.x > 0) {
                        goToPreviousImage();
                      } else {
                        goToNextImage();
                      }
                    }
                  }}
                >
                  {imageLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-muted/20">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  )}
                  <img
                    src={selectedImage}
                    alt={product.name}
                    className="h-full w-full cursor-pointer object-cover transition-transform duration-300"
                    onClick={goToNextImage}
                    title={translate('common.click_to_view_next_image')}
                    onLoad={() => setImageLoading(false)}
                    onError={() => setImageLoading(false)}
                  />
                </motion.div>
              </AnimatePresence>

              {/* Boutons de navigation */}
              {getAllImages.length > 1 && (
                <>
                  {/* Bouton précédent */}
                  <Button
                    variant="outline"
                    size="icon"
                    className="absolute left-2 top-1/2 z-10 h-8 w-8 -translate-y-1/2 rounded-full bg-background/80 backdrop-blur-sm md:h-10 md:w-10"
                    onClick={goToPreviousImage}
                  >
                    <ChevronLeft className="h-4 w-4 md:h-5 md:w-5" />
                    <span className="sr-only">{translate('common.previous_image')}</span>
                  </Button>

                  {/* Bouton suivant */}
                  <Button
                    variant="outline"
                    size="icon"
                    className="absolute right-2 top-1/2 z-10 h-8 w-8 -translate-y-1/2 rounded-full bg-background/80 backdrop-blur-sm md:h-10 md:w-10"
                    onClick={goToNextImage}
                  >
                    <ChevronRight className="h-4 w-4 md:h-5 md:w-5" />
                    <span className="sr-only">{translate('common.next_image')}</span>
                  </Button>

                  {/* Bouton de zoom */}
                  <Button
                    variant="outline"
                    size="icon"
                    className="absolute right-2 top-2 z-10 h-8 w-8 rounded-full bg-background/80 backdrop-blur-sm"
                    onClick={toggleZoom}
                  >
                    <ZoomIn className="h-4 w-4" />
                    <span className="sr-only">{translate('common.zoom_image')}</span>
                  </Button>

                  {/* Indicateur de position */}
                  <div className="absolute bottom-2 left-1/2 z-10 -translate-x-1/2 rounded-full bg-background/80 px-2 py-1 text-xs backdrop-blur-sm">
                    {currentImageIndex + 1} / {getAllImages.length}
                  </div>
                </>
              )}

              {!product.inStock && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                  <span className="rounded-md bg-red-500 px-3 py-1 text-sm font-medium text-white">
                    {translate('common.out_of_stock')}
                  </span>
                </div>
              )}

              {/* Bouton pour revenir aux images du produit principal si on affiche les images d'une variante */}
              {(selectedVariant && selectedVariant.imageUrls.length > 0 && selectedVariant.imageUrls.includes(selectedImage)) && (
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute bottom-2 right-2 flex items-center gap-1 bg-background/80 text-xs backdrop-blur-sm"
                  onClick={() => {
                    if (product.mainImageUrls.length > 0) {
                      setSelectedImage(product.mainImageUrls[0]);
                      setSelectedVariant(null);
                      setSelectedColorVariant(null);
                      // Réinitialiser également les attributs sélectionnés
                      setSelectedAttributes({
                        color: null,
                        size: null,
                        material: null
                      });
                    }
                  }}
                >
                  <RefreshCw className="h-3 w-3" />
                  {translate('common.view_product_images')}
                </Button>
              )}

              {/* Bouton pour revenir aux images du produit principal si on affiche les images d'une variante de couleur */}
              {(!selectedVariant && selectedColorVariant && selectedColorVariant.imageUrls.length > 0 && selectedColorVariant.imageUrls.includes(selectedImage)) && (
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute bottom-2 right-2 flex items-center gap-1 bg-background/80 text-xs backdrop-blur-sm"
                  onClick={() => {
                    if (product.mainImageUrls.length > 0) {
                      setSelectedImage(product.mainImageUrls[0]);
                      setSelectedColorVariant(null);
                      // Réinitialiser également les attributs de couleur
                      setSelectedAttributes({
                        ...selectedAttributes,
                        color: null
                      });
                    }
                  }}
                >
                  <RefreshCw className="h-3 w-3" />
                  {translate('common.view_product_images')}
                </Button>
              )}
            </div>
            <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-primary scrollbar-track-muted">
              {/* Afficher les images de la variante sélectionnée si elle existe et a des images */}
              {/* Utiliser selectedVariant en priorité, sinon utiliser selectedColorVariant */}
              {selectedVariant && selectedVariant.imageUrls.length > 0 ? (
                // Images de la variante sélectionnée
                selectedVariant.imageUrls.map((image, index) => (
                  <motion.button
                    key={`variant-${index}`}
                    className={`relative h-16 w-16 overflow-hidden rounded-md border sm:h-20 sm:w-20 ${
                      selectedImage === image ? 'border-primary ring-2 ring-primary ring-offset-2' : ''
                    } ${index === 0 && selectedImage !== image ? 'ring-2 ring-purple-400 ring-offset-2' : ''}`}
                    onClick={() => {
                      setDirection(currentImageIndex < index ? 1 : -1);
                      setSelectedImage(image);
                    }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                  >
                    <img
                      src={image}
                      alt={`${product.name} - Variante ${index + 1}`}
                      className="h-full w-full object-cover"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-purple-400/80 px-1 py-0.5 text-[8px] font-bold text-purple-950">
                      {translate('common.variant_image')}
                    </div>
                  </motion.button>
                ))
              ) : selectedColorVariant && selectedColorVariant.imageUrls.length > 0 ? (
                // Images de la variante de couleur
                selectedColorVariant.imageUrls.map((image, index) => (
                  <motion.button
                    key={`color-variant-${index}`}
                    className={`relative h-16 w-16 overflow-hidden rounded-md border sm:h-20 sm:w-20 ${
                      selectedImage === image ? 'border-primary ring-2 ring-primary ring-offset-2' : ''
                    } ${index === 0 && selectedImage !== image ? 'ring-2 ring-purple-400 ring-offset-2' : ''}`}
                    onClick={() => {
                      setDirection(currentImageIndex < index ? 1 : -1);
                      setSelectedImage(image);
                    }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                  >
                    <img
                      src={image}
                      alt={`${product.name} - Variante couleur ${index + 1}`}
                      className="h-full w-full object-cover"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-purple-400/80 px-1 py-0.5 text-[8px] font-bold text-purple-950">
                      {translate('common.variant_image')}
                    </div>
                  </motion.button>
                ))
              ) : (
                // Images du produit principal si aucune variante n'est sélectionnée
                <>
                  {/* Afficher d'abord les images principales */}
                  {product.mainImageUrls.map((image, index) => (
                    <motion.button
                      key={`main-${index}`}
                      className={`relative h-16 w-16 overflow-hidden rounded-md border sm:h-20 sm:w-20 ${
                        selectedImage === image ? 'border-primary ring-2 ring-primary ring-offset-2' : ''
                      } ${index < 2 && selectedImage !== image ? 'ring-2 ring-amber-400 ring-offset-2' : ''}`}
                      onClick={() => {
                        setDirection(currentImageIndex < index ? 1 : -1);
                        setSelectedImage(image);
                      }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <img
                        src={image}
                        alt={`${product.name} - Image principale ${index + 1}`}
                        className="h-full w-full object-cover"
                      />
                      {index < 2 && (
                        <div className="absolute bottom-0 left-0 right-0 bg-amber-400/80 px-1 py-0.5 text-[8px] font-bold text-amber-950">
                          {translate('common.main_image')}
                        </div>
                      )}
                    </motion.button>
                  ))}

                  {/* Puis les images additionnelles */}
                  {product.additionalImageUrls.map((image, index) => (
                    <motion.button
                      key={`additional-${index}`}
                      className={`relative h-16 w-16 overflow-hidden rounded-md border sm:h-20 sm:w-20 ${
                        selectedImage === image ? 'border-primary ring-2 ring-primary ring-offset-2' : ''
                      }`}
                      onClick={() => {
                        setDirection(currentImageIndex < index ? 1 : -1);
                        setSelectedImage(image);
                      }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <img
                        src={image}
                        alt={`${product.name} - Image additionnelle ${index + 1}`}
                        className="h-full w-full object-cover"
                      />
                    </motion.button>
                  ))}
                </>
              )}
            </div>
          </div>

          {/* Informations du produit */}
          <div className="space-y-6">
            <div>
              <h1 className="text-2xl font-bold sm:text-3xl">{product.name}</h1>

              {/* Affichage de la marque avec une couleur distinctive */}
              {product.brand && (
                <div className="mt-1">
                  <span className="inline-block rounded-md bg-blue-100 px-2 py-1 text-sm font-medium text-blue-800">
                    {product.brand}
                  </span>
                </div>
              )}

              {/* Affichage du code produit */}
              {product.productCode && (
                <div className="mt-1 text-sm text-muted-foreground">
                  {translate('common.product_code')}: <span className="font-medium">{product.productCode}</span>
                </div>
              )}

              <div className="mt-2 flex items-center gap-2">
                {product.reviews > 0 && (
                  <>
                    <div className="flex items-center text-amber-500">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${i < Math.floor(product.rating) ? 'fill-current' : ''}`}
                        />
                      ))}
                      <span className="ml-1 text-sm font-medium">{product.rating.toFixed(1)}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      ({product.reviews} {translate('common.reviews')})
                    </span>
                  </>
                )}
                {product.seller && (
                  <span className="text-sm text-muted-foreground">
                    {translate('common.sold_by')} <Link href="#" className="hover:underline">{product.seller}</Link>
                  </span>
                )}
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="text-2xl font-bold">{product.formattedPrice()}</div>

              {product.isOnSale() && (
                <>
                  <div className="text-lg text-muted-foreground line-through">
                    {product.formattedOriginalPrice()}
                  </div>
                  <div className="rounded-md bg-red-100 px-2 py-1 text-sm font-medium text-red-800">
                    -{product.getDiscountPercentage()}%
                  </div>
                </>
              )}
            </div>

            <Separator />

            {/* Sélecteur de variantes unifié (attributs du produit + variantes) */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">{translate('common.product_options')}</h3>

                {/* Bouton du guide des tailles - affiché si des tailles existent (produit ou variantes) */}
                {(product.attributes.some(a => a.type === 'taille') ||
                  product.variants.some(v => v.attributes.some(a => a.type === 'taille'))) && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center gap-1 text-xs text-primary"
                    onClick={() => setIsSizeGuideOpen(true)}
                  >
                    <Ruler className="h-3 w-3" />
                    {translate('common.view_size_guide')}
                  </Button>
                )}
              </div>

              <ProductVariantSelector
                variants={product.variants}
                basePrice={product.getCurrentPrice()}
                currency={product.currency}
                onVariantChange={setSelectedVariant}
                product={product}
                onImageChange={setSelectedImage}
                onAttributesSelected={setSelectedAttributes}
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm">
                <Truck className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-600">
                  {product.inStock ? translate('common.in_stock') : translate('common.currently_unavailable')}
                </span>
              </div>

              {/* Zone de livraison */}
              <div className="rounded-md border p-4">
                <div className="mb-2 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <h3 className="text-sm font-medium">{translate('common.delivery_zone')}</h3>
                </div>

                {/* Afficher un bouton pour voir les zones disponibles pour ce produit */}
                <ProductAvailableZones product={product} compact={true} />

                {selectedZone && (
                  <div className="mt-4 space-y-2">
                    <Separator />
                    <div className="flex items-center justify-between pt-2">
                      <span className="text-sm">{translate('common.selected_zone')}:</span>
                      <span className="text-sm font-medium">{selectedZone.nom}</span>
                    </div>
                    {/* N'afficher DeliveryInfo que si une zone est sélectionnée */}
                    <DeliveryInfo product={product} />
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2 w-full"
                        >
                          {translate('common.change_delivery_zone')}
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>{translate('common.change_delivery_zone')}</DialogTitle>
                        </DialogHeader>
                        <ZoneLivraisonSelector />
                      </DialogContent>
                    </Dialog>
                  </div>
                )}

                
              </div>

              <div className="flex flex-wrap items-center gap-4 ">
                <div className="flex items-center rounded-md border">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={decreaseQuantity}
                    disabled={quantity <= 1 || !product.inStock}
                    className="h-10 w-10 rounded-none"
                  >
                    <Minus className="h-4 w-4" />
                    <span className="sr-only">{translate('common.decrease_quantity')}</span>
                  </Button>
                  <span className="w-12 text-center">{quantity}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={increaseQuantity}
                    disabled={!product.inStock}
                    className="h-10 w-10 rounded-none"
                  >
                    <Plus className="h-4 w-4" />
                    <span className="sr-only">{translate('common.increase_quantity')}</span>
                  </Button>
                </div>
                <Button
                  onClick={handleAddToCart}
                  disabled={!product.inStock || isAddingToCart}
                  className="flex-1"
                >
                  {isAddingToCart ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {translate('common.adding_to_cart')}
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="mr-2 h-4 w-4" />
                      {translate('common.add_to_cart')}
                    </>
                  )}
                </Button>
                <WishlistButton
                  product={product}
                  variant="outline"
                  size="icon"
                  disabled={!product.inStock}
                />
                <Button variant="outline" size="icon">
                  <Share2 className="h-4 w-4" />
                  <span className="sr-only">{translate('common.share')}</span>
                </Button>
              </div>
            </div>

            <Separator />

            {/* Onglets d'information */}
            <Tabs defaultValue="description">
              <TabsList className="w-full">
                <TabsTrigger value="description" className="flex-1">{translate('common.description')}</TabsTrigger>
                <TabsTrigger value="details" className="flex-1">{translate('common.details')}</TabsTrigger>
                <TabsTrigger value="reviews" className="flex-1">{translate('reviews.title')}</TabsTrigger>
              </TabsList>
              <TabsContent value="description" className="mt-4">
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <p>{product.description}</p>
                </div>
              </TabsContent>
              <TabsContent value="details" className="mt-4">
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="font-medium">{translate('common.brand')}</div>
                    <div className="font-semibold text-blue-700">{product.brand || product.seller || translate('common.unknown')}</div>
                  </div>
                  {product.productCode && (
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="font-medium">{translate('common.product_code')}</div>
                      <div>{product.productCode}</div>
                    </div>
                  )}
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="font-medium">{translate('common.category')}</div>
                    <div>{category ? category.getTranslatedName() : translate('common.uncategorized')}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="font-medium">{translate('common.availability')}</div>
                    <div>{product.inStock ? translate('common.available') : translate('common.unavailable')}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="font-medium">{translate('common.reference')}</div>
                    <div>REF-{product.id}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="font-medium">{translate('common.seller')}</div>
                    <div>{product.seller || translate('common.unknown')}</div>
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="reviews" className="mt-4">
                {/* Utiliser le nouveau composant ReviewsList */}
                <ReviewsList
                  productId={product.id}
                  productName={product.name}
                  initialReviewsCount={product.reviews}
                />
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Produits similaires */}
        {product && category && (
          <section className="mt-16">
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-2xl font-bold">{translate('common.similar_products')}</h2>
            </div>

            <ProductListInfinite
              categoryId={category.id}
              initialProducts={similarProducts}
              itemsPerPage={8}
              emptyMessage={translate('common.no_similar_products')}
              className="min-h-[200px]"
              filters={{
                exclude_product: product.id
              }}
              showPagination={true}
            />
          </section>
        )}
      </div>

      {/* Modal du guide des tailles */}
      <SizeGuideModal
        isOpen={isSizeGuideOpen}
        onClose={() => setIsSizeGuideOpen(false)}
      />
    </EcommerceLayout>
  );
}
