import { CartItem, DeliveryInfo } from '../models/CartItem';
import { Product } from '../models/Product';

/**
 * Service pour gérer les opérations liées au panier d'achat
 *
 * Cette classe fournit des méthodes pour ajouter, supprimer et modifier les éléments du panier
 */
export class CartService {
  private items: CartItem[] = [];
  private storageKey = 'shopping_cart';

  /**
   * Crée une nouvelle instance du service de panier
   *
   * Charge les éléments du panier depuis le stockage local si disponible
   */
  constructor() {
    this.loadCart();
  }

  /**
   * Récupère tous les éléments du panier
   *
   * @returns Un tableau d'éléments du panier
   */
  getItems(): CartItem[] {
    return [...this.items];
  }

  /**
   * Ajoute un produit au panier
   *
   * Chaque combinaison unique d'attributs (couleur, taille, etc.) est considérée comme un élément distinct
   *
   * @param product - Le produit à ajouter
   * @param quantity - La quantité à ajouter (par défaut: 1)
   * @param deliveryInfo - Les informations de livraison pour ce produit (optionnel)
   */
  addItem(product: Product, quantity: number = 1, deliveryInfo?: DeliveryInfo): void {
    // Générer la clé pour le produit à ajouter
    const productKey = this.getProductVariantKey(product);

    // Chercher un élément existant avec la même combinaison d'attributs
    const existingItem = this.items.find(item => {
      const itemKey = this.getProductVariantKey(item.product);
      return itemKey === productKey;
    });

    if (existingItem) {
      // Si un élément avec la même combinaison d'attributs existe déjà, augmenter sa quantité
      existingItem.increaseQuantity(quantity);

      // Mettre à jour les informations de livraison si elles sont fournies
      if (deliveryInfo) {
        existingItem.deliveryInfo = deliveryInfo;
      }
    } else {
      // Sinon, ajouter un nouvel élément au panier avec les informations de livraison
      this.items.push(new CartItem(product, quantity, deliveryInfo));
    }

    this.saveCart();
  }

  /**
   * Génère une clé unique pour un produit basée sur ses attributs
   *
   * @param prod - Le produit pour lequel générer une clé
   * @returns Une chaîne représentant la combinaison unique d'attributs
   * @private
   */
  private getProductVariantKey(prod: Product): string {
    // Extraire les attributs de couleur, taille et matière
    const colorAttr = prod.attributes.find(a => a.type === 'couleur');
    const sizeAttr = prod.attributes.find(a => a.type === 'taille');
    const materialAttr = prod.attributes.find(a => a.type === 'matiere');

    // Créer une chaîne qui représente la combinaison unique d'attributs
    let variantKey = prod.id;

    if (colorAttr && 'nom' in colorAttr) variantKey += `-color:${colorAttr.nom}`;
    if (sizeAttr && 'valeur' in sizeAttr) variantKey += `-size:${sizeAttr.valeur}`;
    if (materialAttr && 'valeur' in materialAttr) variantKey += `-material:${materialAttr.valeur}`;

    return variantKey;
  }

  /**
   * Supprime un élément du panier
   *
   * @param productId - L'identifiant du produit à supprimer
   * @param variantKey - Clé optionnelle de la variante à supprimer (si non fournie, supprime toutes les variantes du produit)
   */
  removeItem(productId: string, variantKey?: string): void {
    if (variantKey) {
      // Supprimer uniquement la variante spécifique
      this.items = this.items.filter(item => {
        const itemKey = this.getProductVariantKey(item.product);
        return itemKey !== variantKey;
      });
    } else {
      // Comportement par défaut: supprimer toutes les variantes du produit
      this.items = this.items.filter(item => item.product.id !== productId);
    }
    this.saveCart();
  }

  /**
   * Met à jour la quantité d'un élément du panier
   *
   * @param productId - L'identifiant du produit à mettre à jour
   * @param quantity - La nouvelle quantité
   * @param variantKey - Clé optionnelle de la variante à mettre à jour
   */
  updateQuantity(productId: string, quantity: number, variantKey?: string): void {
    let item;

    if (variantKey) {
      // Trouver la variante spécifique
      item = this.items.find(item => {
        const itemKey = this.getProductVariantKey(item.product);
        return itemKey === variantKey;
      });
    } else {
      // Comportement par défaut: trouver par ID de produit
      item = this.items.find(item => item.product.id === productId);
    }

    if (item) {
      if (quantity <= 0) {
        this.removeItem(productId, variantKey);
      } else {
        item.quantity = quantity;
        this.saveCart();
      }
    }
  }

  /**
   * Vide complètement le panier
   */
  clearCart(): void {
    this.items = [];
    this.saveCart();
  }

  /**
   * Calcule le sous-total du panier (sans frais de livraison)
   *
   * @returns Le sous-total du panier
   */
  getSubtotal(): number {
    return this.items.reduce((total, item) => total + item.getSubtotal(), 0);
  }

  /**
   * Calcule les frais de livraison totaux du panier
   *
   * @returns Les frais de livraison totaux
   */
  getDeliveryFees(): number {
    console.log("Calcul des frais de livraison totaux...");
    const fees = this.items.reduce((total, item) => {
      const itemFees = item.getDeliveryFees();
      console.log(`Frais pour ${item.product.name}: ${itemFees}`);
      return total + itemFees;
    }, 0);
    console.log(`Total des frais de livraison: ${fees}`);
    return fees;
  }

  /**
   * Calcule le total du panier (incluant les frais de livraison)
   *
   * @returns Le montant total du panier
   */
  getTotal(): number {
    return this.getSubtotal() + this.getDeliveryFees();
  }

  /**
   * Formate le total du panier avec le symbole de devise
   *
   * Note: Cette méthode utilise la devise de chaque produit individuellement
   * et ne convertit pas les prix. Si le panier contient des produits avec
   * différentes devises, le total affichera "Devises multiples".
   *
   * @returns Le total formaté avec le symbole de devise
   */
  formattedTotal(): string {
    // Vérifier si le panier est vide
    if (this.items.length === 0) {
      return `0 FCFA`;
    }

    // Vérifier si tous les produits ont la même devise
    const currencies = new Set();

    // Collecter toutes les devises non vides
    this.items.forEach(item => {
      if (item.product && item.product.currency) {
        currencies.add(item.product.currency);
      }
    });

    if (currencies.size === 0) {
      return `${this.getTotal().toFixed(0)} FCFA`;
    } else if (currencies.size === 1) {
      // Si tous les produits ont la même devise, utiliser cette devise
      const currency = Array.from(currencies)[0] as string;
      return `${this.getTotal().toFixed(0)} ${currency}`;
    } else {
      // Si les produits ont des devises différentes
      return `${this.getTotal().toFixed(0)} (Devises multiples)`;
    }
  }

  /**
   * Compte le nombre total d'articles dans le panier
   *
   * @returns Le nombre total d'articles
   */
  getItemCount(): number {
    return this.items.reduce((count, item) => count + item.quantity, 0);
  }

  /**
   * Sauvegarde l'état du panier dans le stockage local
   *
   * @private
   */
  private saveCart(): void {
    if (typeof window !== 'undefined') {
      // Afficher un message de débogage pour vérifier que les produits sont correctement sauvegardés
      console.log('Sauvegarde du panier dans localStorage:', this.items);

      localStorage.setItem(this.storageKey, JSON.stringify(this.items));
    }
  }

  /**
   * Charge l'état du panier depuis le stockage local
   *
   * @private
   */
  private loadCart(): void {
    if (typeof window !== 'undefined') {
      const savedCart = localStorage.getItem(this.storageKey);

      if (savedCart) {
        try {
          const parsedCart = JSON.parse(savedCart);
          this.items = parsedCart.map((item: any) => {
            // Récupérer toutes les propriétés du produit avec des valeurs par défaut si nécessaire
            const product = new Product(
              item.product.id || '',
              item.product.name || '',
              item.product.slug || '',
              item.product.productCode || null,
              item.product.brand || null,
              item.product.description || '',
              Number(item.product.price) || 0,
              item.product.currency || 'FCFA',  // Utiliser la devise stockée ou FCFA par défaut
              item.product.discountPrice ? Number(item.product.discountPrice) : null,
              item.product.discountStartDate ? new Date(item.product.discountStartDate) : null,
              item.product.discountEndDate ? new Date(item.product.discountEndDate) : null,
              item.product.imageUrl || '',
              item.product.imageUrls || [],
              item.product.category || '',
              item.product.inStock !== undefined ? item.product.inStock : true,
              item.product.rating || 0,
              item.product.reviews || 0,
              item.product.seller || '',
              item.product.mainImageUrls || [],
              item.product.additionalImageUrls || [],
              item.product.attributes || [],
              item.product.variants || []
            );

            // Vérifier si les propriétés importantes sont correctement chargées
            if (!product.imageUrl && item.product.imageUrl) {
              product.imageUrl = item.product.imageUrl;
            }

            if (!product.imageUrls || product.imageUrls.length === 0) {
              if (item.product.imageUrls && item.product.imageUrls.length > 0) {
                product.imageUrls = item.product.imageUrls;
              } else if (item.product.imageUrl) {
                product.imageUrls = [item.product.imageUrl];
              }
            }

            if (!product.mainImageUrls || product.mainImageUrls.length === 0) {
              if (item.product.mainImageUrls && item.product.mainImageUrls.length > 0) {
                product.mainImageUrls = item.product.mainImageUrls;
              } else if (product.imageUrls && product.imageUrls.length > 0) {
                product.mainImageUrls = product.imageUrls.slice(0, 2);
              }
            }

            // S'assurer que le prix est un nombre
            if (isNaN(product.price)) {
              product.price = 0;
            }

            // S'assurer que la devise est définie
            if (!product.currency) {
              product.currency = 'FCFA';
            }

            // Récupérer les informations de livraison si elles existent
            let deliveryInfo = undefined;
            if (item.deliveryInfo) {
              console.log("Informations de livraison trouvées dans localStorage:", item.deliveryInfo);

              // Vérifier si les frais de livraison sont définis
              const frais_livraison = item.deliveryInfo.frais_livraison !== undefined ?
                Number(item.deliveryInfo.frais_livraison) : 0;

              // Vérifier si les frais spécifiques sont définis
              const frais_specifiques = item.deliveryInfo.frais_livraison_specifique !== undefined ?
                Number(item.deliveryInfo.frais_livraison_specifique) : undefined;

              deliveryInfo = {
                frais_livraison: frais_livraison,
                delai_livraison_min: item.deliveryInfo.delai_livraison_min || 0,
                delai_livraison_max: item.deliveryInfo.delai_livraison_max || 0,
                frais_livraison_specifique: frais_specifiques,
                zone_id: item.deliveryInfo.zone_id,
                zone_nom: item.deliveryInfo.zone_nom
              };

              console.log("Informations de livraison reconstruites:", deliveryInfo);
            }

            return new CartItem(product, item.quantity || 1, deliveryInfo);
          });

          // Afficher un message de débogage pour vérifier que les produits sont correctement chargés
          console.log('Panier chargé depuis localStorage:', this.items);
        } catch (error) {
          console.error('Erreur lors du chargement du panier:', error);
          this.items = [];
        }
      }
    }
  }
}
