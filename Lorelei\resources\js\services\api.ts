import axios, { AxiosError, AxiosResponse } from 'axios';
import { toast } from '@/components/ui/use-toast';

// Avec Vite, on utilise import.meta.env au lieu de process.env
const api = axios.create({
  baseURL: '/api',
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  }
});

// Intercepteur de requête pour ajouter le token d'authentification
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur de réponse pour gérer les erreurs
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Afficher les messages de succès si présents dans la réponse
    if (response.data?.message && response.status >= 200 && response.status < 300) {
      toast({
        title: 'Succès',
        description: response.data.message,
        variant: 'default',
      });
    }
    return response;
  },
  (error: AxiosError) => {
    // Gérer les différents types d'erreurs
    if (!error.response) {
      // Erreur réseau (pas de réponse du serveur)
      toast({
        title: 'Erreur de connexion',
        description: 'Impossible de se connecter au serveur. Veuillez vérifier votre connexion internet.',
        variant: 'destructive',
      });
    } else {
      const status = error.response.status;
      const data = error.response.data as any;

      // Gérer les différents codes d'erreur
      switch (status) {
        case 401:
          // Non authentifié
          toast({
            title: 'Session expirée',
            description: 'Votre session a expiré. Veuillez vous reconnecter.',
            variant: 'destructive',
          });
          // Rediriger vers la page de connexion API
          localStorage.removeItem('auth_token');
          window.location.href = route('login.api');
          break;

        case 403:
          // Accès interdit
          toast({
            title: 'Accès refusé',
            description: data.message || 'Vous n\'avez pas les droits nécessaires pour effectuer cette action.',
            variant: 'destructive',
          });
          break;

        case 404:
          // Ressource non trouvée
          toast({
            title: 'Non trouvé',
            description: data.message || 'La ressource demandée n\'existe pas.',
            variant: 'destructive',
          });
          break;

        case 422:
          // Erreur de validation
          toast({
            title: 'Erreur de validation',
            description: data.message || 'Veuillez vérifier les informations saisies.',
            variant: 'destructive',
          });
          break;

        case 500:
        case 502:
        case 503:
        case 504:
          // Erreurs serveur
          toast({
            title: 'Erreur serveur',
            description: data.message || 'Une erreur est survenue sur le serveur. Veuillez réessayer plus tard.',
            variant: 'destructive',
          });
          break;

        default:
          // Autres erreurs
          toast({
            title: 'Erreur',
            description: data.message || 'Une erreur est survenue. Veuillez réessayer.',
            variant: 'destructive',
          });
          break;
      }
    }

    // Journaliser l'erreur en mode développement
    if (import.meta.env.DEV) {
      console.error('API Error:', error);
    }

    return Promise.reject(error);
  }
);

  export default api;

  export const backendUrlImageUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000/';
