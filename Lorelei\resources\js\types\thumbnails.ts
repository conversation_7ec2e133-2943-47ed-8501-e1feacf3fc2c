/**
 * Types TypeScript pour les miniatures
 */

/**
 * Tailles de miniatures disponibles
 */
export type ThumbnailSize = 'small' | 'medium' | 'large';

/**
 * URLs de miniatures pour un élément unique (catégorie, bannière)
 */
export interface SingleThumbnailUrls {
  small: string | null;
  medium: string | null;
  large: string | null;
}

/**
 * URLs de miniatures pour des éléments multiples (produits, reviews)
 */
export interface MultipleThumbnailUrls {
  small: string[];
  medium: string[];
  large: string[];
}

/**
 * Configuration des tailles de miniatures
 */
export interface ThumbnailConfig {
  width: number;
  height: number;
}

/**
 * Configuration des tailles par type de contenu
 */
export interface ThumbnailSizeConfig {
  [key: string]: {
    [size in ThumbnailSize]: ThumbnailConfig;
  };
}

/**
 * Configuration par défaut des tailles de miniatures
 */
export const DEFAULT_THUMBNAIL_SIZES: ThumbnailSizeConfig = {
  products: {
    small: { width: 150, height: 150 },
    medium: { width: 300, height: 300 },
    large: { width: 600, height: 600 }
  },
  categories: {
    small: { width: 100, height: 100 },
    medium: { width: 200, height: 200 },
    large: { width: 400, height: 400 }
  },
  banners: {
    small: { width: 300, height: 128 },
    medium: { width: 600, height: 257 },
    large: { width: 1200, height: 514 }
  },
  reviews: {
    small: { width: 100, height: 100 },
    medium: { width: 200, height: 200 },
    large: { width: 400, height: 400 }
  }
};

/**
 * Utilitaires pour les miniatures
 */
export class ThumbnailUtils {
  /**
   * Obtient l'URL de la miniature ou l'image originale en fallback
   */
  static getThumbnailOrOriginal(
    thumbnailUrls: SingleThumbnailUrls | MultipleThumbnailUrls,
    size: ThumbnailSize,
    originalUrl: string | string[],
    index: number = 0
  ): string {
    // Pour les miniatures multiples (produits, reviews)
    if (Array.isArray((thumbnailUrls as MultipleThumbnailUrls)[size])) {
      const urls = (thumbnailUrls as MultipleThumbnailUrls)[size];
      if (urls && urls[index]) {
        return urls[index];
      }
      // Fallback vers l'image originale
      if (Array.isArray(originalUrl)) {
        return originalUrl[index] || originalUrl[0] || '';
      }
      return originalUrl as string || '';
    }

    // Pour les miniatures uniques (catégories, bannières)
    const url = (thumbnailUrls as SingleThumbnailUrls)[size];
    if (url) {
      return url;
    }
    // Fallback vers l'image originale
    return Array.isArray(originalUrl) ? originalUrl[0] || '' : originalUrl as string || '';
  }

  /**
   * Vérifie si des miniatures sont disponibles
   */
  static hasThumbnails(thumbnailUrls: SingleThumbnailUrls | MultipleThumbnailUrls): boolean {
    if (Array.isArray((thumbnailUrls as MultipleThumbnailUrls).small)) {
      const urls = thumbnailUrls as MultipleThumbnailUrls;
      return urls.small.length > 0 || urls.medium.length > 0 || urls.large.length > 0;
    }

    const urls = thumbnailUrls as SingleThumbnailUrls;
    return !!(urls.small || urls.medium || urls.large);
  }

  /**
   * Obtient la meilleure taille de miniature disponible
   */
  static getBestAvailableSize(
    thumbnailUrls: SingleThumbnailUrls | MultipleThumbnailUrls,
    preferredSize: ThumbnailSize = 'medium'
  ): ThumbnailSize | null {
    const sizes: ThumbnailSize[] = ['small', 'medium', 'large'];
    const startIndex = sizes.indexOf(preferredSize);

    // Vérifier d'abord la taille préférée, puis les autres
    const orderedSizes = [
      ...sizes.slice(startIndex),
      ...sizes.slice(0, startIndex)
    ];

    for (const size of orderedSizes) {
      if (Array.isArray((thumbnailUrls as MultipleThumbnailUrls)[size])) {
        const urls = (thumbnailUrls as MultipleThumbnailUrls)[size];
        if (urls && urls.length > 0) {
          return size;
        }
      } else {
        const url = (thumbnailUrls as SingleThumbnailUrls)[size];
        if (url) {
          return size;
        }
      }
    }

    return null;
  }

  /**
   * Génère un srcset pour les images responsives
   */
  static generateSrcSet(
    thumbnailUrls: SingleThumbnailUrls | MultipleThumbnailUrls,
    index: number = 0
  ): string {
    const srcSet: string[] = [];

    if (Array.isArray((thumbnailUrls as MultipleThumbnailUrls).small)) {
      const urls = thumbnailUrls as MultipleThumbnailUrls;
      if (urls.small[index]) srcSet.push(`${urls.small[index]} 150w`);
      if (urls.medium[index]) srcSet.push(`${urls.medium[index]} 300w`);
      if (urls.large[index]) srcSet.push(`${urls.large[index]} 600w`);
    } else {
      const urls = thumbnailUrls as SingleThumbnailUrls;
      if (urls.small) srcSet.push(`${urls.small} 150w`);
      if (urls.medium) srcSet.push(`${urls.medium} 300w`);
      if (urls.large) srcSet.push(`${urls.large} 600w`);
    }

    return srcSet.join(', ');
  }
}

/**
 * Hook personnalisé pour utiliser les miniatures (pour Vue/React)
 */
export interface UseThumbnailsReturn {
  getThumbnail: (size: ThumbnailSize, index?: number) => string;
  hasThumbnails: boolean;
  generateSrcSet: (index?: number) => string;
  getBestSize: (preferredSize?: ThumbnailSize) => ThumbnailSize | null;
}

/**
 * Fonction utilitaire pour créer un hook de miniatures
 */
export function createThumbnailsHook(
  thumbnailUrls: SingleThumbnailUrls | MultipleThumbnailUrls,
  originalUrls: string | string[]
): UseThumbnailsReturn {
  return {
    getThumbnail: (size: ThumbnailSize, index: number = 0) =>
      ThumbnailUtils.getThumbnailOrOriginal(thumbnailUrls, size, originalUrls, index),
    
    hasThumbnails: ThumbnailUtils.hasThumbnails(thumbnailUrls),
    
    generateSrcSet: (index: number = 0) =>
      ThumbnailUtils.generateSrcSet(thumbnailUrls, index),
    
    getBestSize: (preferredSize: ThumbnailSize = 'medium') =>
      ThumbnailUtils.getBestAvailableSize(thumbnailUrls, preferredSize)
  };
}
