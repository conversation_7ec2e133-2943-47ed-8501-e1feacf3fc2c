# Plateforme Marchand - seller.lorelei.com

## Vue d'ensemble du projet

Ce document décrit l'architecture et les fonctionnalités de la plateforme dédiée aux marchands de Lorelei, accessible via `seller.lorelei.com`. Cette plateforme est un projet Laravel + React séparé du site principal, conçu pour gérer l'inscription, la validation et la gestion des marchands.

## Architecture générale

### Domaines et sous-domaines
- **seller.lorelei.com** : Plateforme dédiée aux marchands (projet séparé)
- **admin.lorelei.com** : Panel d'administration (Filament)
- **lorelei.com** : Site principal e-commerce

### Processus de transition Utilisateur → Marchand
Un utilisateur existant peut devenir marchand via un processus de validation en plusieurs étapes, géré dans le projet marchand séparé.

## Structure de la Landing Page (seller.lorelei.com)

### Header Navigation
- **Pricing** : Tarifs et abonnements
- **Services** : Services offerts aux marchands
- **Resources** : Guides, FAQ, documentation
- **Login** : Connexion pour marchands existants
- **Register** : Inscription nouveau marchand

### Page d'accueil (Landing Page)
Inspirée des marketplaces comme Amazon Seller Central et Shein :

#### Section Hero
- **Titre principal** : "Vendez avec Lorelei - La marketplace qui fait grandir votre business"
- **Sous-titre** : "Rejoignez des milliers de marchands qui font confiance à Lorelei pour développer leurs ventes"
- **CTA principal** : "Commencer à vendre" / "Créer mon compte marchand"
- **Statistiques clés** :
  - "10,000+ marchands actifs"
  - "500,000+ produits vendus"
  - "98% de satisfaction marchands"

#### Section Avantages
- **Visibilité maximale** : "Accédez à des milliers de clients potentiels"
- **Outils puissants** : "Tableau de bord complet, analytics avancées"
- **Support dédié** : "Équipe support disponible 24/7"
- **Paiements sécurisés** : "Orange Money, MTN Money, virements bancaires"

#### Section Comment ça marche
1. **Inscrivez-vous** : "Créez votre compte en quelques minutes"
2. **Ajoutez vos produits** : "Importez ou ajoutez vos produits facilement"
3. **Commencez à vendre** : "Recevez vos premières commandes"

#### Section Témoignages
- Témoignages de marchands existants avec photos et résultats

#### Section Pricing
- Aperçu des 4 niveaux d'abonnement avec prix et avantages principaux

## Processus d'inscription marchand

### Étape 1 : Page de bienvenue (après connexion utilisateur)
**URL** : `/welcome-seller`

**Contenu** :
- **Titre** : "Bienvenue dans l'aventure Lorelei !"
- **Email affiché** : "Connecté en tant que : <EMAIL>"
- **Description du processus** :
  ```
  Pour devenir marchand sur Lorelei, suivez ces 3 étapes simples :

  1️⃣ Répondre à quelques questions
     Informations sur votre business et localisation

  2️⃣ Fournir des documents
     Documents d'identité et informations bancaires

  3️⃣ Être vérifié et commencer à vendre
     Notre équipe valide votre candidature sous 48h
  ```
- **Bouton CTA** : "Commencer mon inscription"

### Étape 2 : Formulaire d'informations business
**URL** : `/seller-application/business-info`

#### Section 1 : Localisation
- **Pays du business** (select obligatoire)
  - Cameroun, Côte d'Ivoire, Sénégal, etc.
- **Ville** (input text obligatoire)
- **Description** : "Indiquez où se trouve physiquement votre business pour la logistique et la fiscalité"

#### Section 2 : Type de business
**Radio buttons avec descriptions** :

- **🏪 Individuel / Auto-entrepreneur**
  - "Vous vendez en votre nom propre"
  - Documents requis : CNI, téléphone, IBAN/Mobile Money

- **🏢 Entreprise / SARL**
  - "Société constituée avec statuts juridiques"
  - Documents requis : Registre de commerce, statuts, CNI dirigeant, IBAN

- **🏛️ Coopérative / Association**
  - "Structure collective ou associative"
  - Documents requis : Récépissé, statuts, CNI représentant, IBAN

- **🏭 Grande entreprise**
  - "Entreprise établie avec chiffre d'affaires important"
  - Documents requis : Bilan comptable, registre commerce, IBAN

#### Section 3 : Prérequis (affichage conditionnel)
Quand l'utilisateur sélectionne un type, affichage des documents requis :

**Pour Individuel** :
```
📋 Documents à préparer :
✓ Carte d'identité nationale (CNI) ou récépissé
✓ Numéro de téléphone valide
✓ Photo tenant votre CNI en main
✓ Informations de paiement :
  - IBAN bancaire (sécurisé et crypté) OU
  - Numéro Orange Money OU
  - Numéro MTN Money
```

**Bouton** : "Continuer vers les documents"

### Étape 3 : Upload des documents
**URL** : `/seller-application/documents`

Formulaire d'upload avec validation en temps réel et prévisualisation.

## Niveaux d'abonnement

### Modèle de Base (Gratuit)
- **Commission** : 5-10% par transaction
- **Avantages** : Accès basique, support email 48h
- **Limites** : Visibilité réduite, pas d'analytics avancées

### Abonnement Basique (32,797.85 FCFA/mois)
- **Commission réduite** : 4-8%
- **Visibilité améliorée** : Priorité modérée dans recherches
- **Support prioritaire** : 24h email + chat
- **Analytics** : Rapports de base
- **Réduction logistique** : 5%

### Abonnement Premium (65,595.70 FCFA/mois)
- **Commission** : 3-6%
- **Visibilité premium** : Badge "Marchand Premium"
- **Analytics avancées** : Rapports détaillés, prévisions
- **Support VIP** : 12h + gestionnaire dédié
- **Marketing** : 1 campagne sponsorisée/mois
- **Réduction logistique** : 10%

### Abonnement Élite (131,191.40 FCFA/mois)
- **Commission minimale** : 2-4%
- **Visibilité maximale** : Badge "Marchand Élite"
- **IA prédictive** : Tendances et recommandations
- **Support exclusif** : 6h + 24/7
- **Marketing avancé** : 3 campagnes + bannières
- **Accès exclusif** : Événements, partenariats
- **Réduction logistique** : 15% + priorité

## Pages supplémentaires

### FAQ
- Questions fréquentes sur l'inscription
- Processus de validation
- Gestion des commissions
- Support technique

### Resources
- Guides de démarrage
- Bonnes pratiques de vente
- Tutoriels vidéo
- Documentation API

## Intégration avec le projet principal

### Base de données partagée
- Les deux projets partagent la même base de données
- Synchronisation des données marchands
- Gestion des statuts de validation

### API de communication
- Endpoints pour synchroniser les données
- Webhooks pour les changements de statut
- API pour la validation des documents

## Sécurité et validation

### Validation des documents
- Vérification automatique des formats
- Validation manuelle par l'équipe
- Système de notifications par email/SMS

### Sécurité des données
- Cryptage des informations bancaires
- Stockage sécurisé des documents
- Conformité RGPD/protection des données

## Technologies utilisées

### Frontend
- **React** avec TypeScript
- **Tailwind CSS** pour le styling
- **Inertia.js** pour la communication Laravel-React

### Backend
- **Laravel 11** avec PHP 8.2+
- **Filament** pour l'administration
- **MySQL** base de données partagée

### Stockage
- **Laravel Storage** pour les documents
- **CDN** pour les images optimisées

## Structure de base de données

### Tables principales créées

#### 1. Extension de la table `marchands`
Nouvelles colonnes ajoutées pour la plateforme seller :
- **Localisation** : `pays_business`, `ville_business`
- **Type de business** : `type_business` (individuel, entreprise, cooperative, grande_entreprise)
- **Statut et étapes** : `statut_validation`, `etape_inscription`
- **Documents** : `documents_soumis`, `documents_requis`, `date_soumission_documents`
- **Validation** : `date_validation`, `commentaires_validation`, `validateur_id`
- **Contact** : `telephone_principal`, `telephone_secondaire`, `email_business`, `site_web`
- **Paiement** : `methode_paiement_preferee`, `iban_crypte`, `numero_orange_money`, `numero_mtn_money`
- **Business** : `description_business`, `categories_produits`, `chiffre_affaires_estime`, `nombre_employes`
- **Préférences** : `accepte_conditions`, `accepte_newsletter`, `langue_preferee`
- **Parrainage** : `code_parrainage`, `parrain_id`

#### 2. Table `marchand_abonnements`
Gestion complète des abonnements :
- **Types** : gratuit, basique, premium, elite
- **Statuts** : actif, expire, suspendu, annule, en_attente
- **Tarification** : prix_mensuel, commission_taux_min/max, reduction_logistique
- **Limites** : limite_produits, limite_commandes_mois, limite_campagnes_mois
- **Fonctionnalités** : acces_analytics_avancees, acces_support_prioritaire, etc.
- **Facturation** : mode_facturation, facturation_automatique, methode_paiement_abonnement
- **Essais** : est_periode_essai, fin_periode_essai, code_promotion

#### 3. Table `marchand_abonnement_historique`
Traçabilité complète des changements :
- **Actions** : creation, upgrade, downgrade, renouvellement, suspension, etc.
- **Transitions** : type_abonnement_avant/apres, statut_avant/apres
- **Financier** : prix_avant/apres, montant_paye, montant_rembourse
- **Métadonnées** : raison, initie_par, reference_paiement, adresse_ip

#### 4. Table `marchand_documents`
Gestion sécurisée des documents :
- **Types** : CNI, passeport, registre_commerce, rib_bancaire, etc.
- **Fichiers** : nom_original, chemin_fichier, taille_fichier, hash_fichier
- **Validation** : statut_validation, validateur_id, commentaires_validation
- **Sécurité** : est_crypte, cle_cryptage, est_confidentiel
- **OCR** : ocr_effectue, donnees_ocr, score_qualite

## Modèles Laravel créés

### 1. MarchandAbonnement
- Gestion des abonnements avec configuration automatique
- Méthodes utilitaires : `estActif()`, `calculerCommission()`, `vaExpirer()`
- Factory method : `creerAbonnement()` avec configuration par défaut

### 2. MarchandAbonnementHistorique
- Enregistrement automatique des actions
- Méthodes statiques : `enregistrerChangementAbonnement()`, `enregistrerPaiement()`
- Rapports et analytics : `getResumeMarchand()`

### 3. MarchandDocument
- Upload et validation sécurisés
- Méthodes : `valider()`, `rejeter()`, `getUrlSecurisee()`
- Types de documents dynamiques selon le type de business

### 4. Extension du modèle Marchand
- Nouvelles relations : `abonnementActuel()`, `documents()`, `validateur()`
- Méthodes utilitaires : `estValide()`, `documentsObligatoiresComplets()`
- Logique métier : `getDocumentsRequis()` selon le type de business

## Migrations créées

1. **2024_12_20_000001_extend_marchands_table_for_seller_platform.php**
   - Extension de la table marchands existante
   - Ajout de toutes les colonnes nécessaires pour le processus d'inscription
   - Index optimisés pour les requêtes fréquentes

2. **2024_12_20_000002_create_marchand_abonnements_table.php**
   - Table complète pour la gestion des abonnements
   - Configuration flexible des fonctionnalités par niveau
   - Support des promotions et périodes d'essai

3. **2024_12_20_000003_create_marchand_abonnement_historique_table.php**
   - Traçabilité complète des changements d'abonnement
   - Audit trail pour la facturation et les remboursements

4. **2024_12_20_000004_create_marchand_documents_table.php**
   - Stockage sécurisé des documents avec métadonnées
   - Support OCR et validation automatique
   - Gestion des versions et de l'expiration

## Seeder de test

**MarchandSellerPlatformSeeder.php** :
- Création de 4 marchands de test avec différents statuts
- Abonnements configurés selon les profils
- Historique d'actions pour démonstration
- Données réalistes pour les tests

## Prochaines étapes d'implémentation

### Phase 1 : Base technique (Actuel)
✅ Structure de base de données
✅ Modèles Laravel avec relations
✅ Migrations et seeders

### Phase 2 : API et Backend
- Controllers pour l'inscription marchand
- API endpoints pour le projet seller séparé
- Services de validation des documents
- Système de notifications (email/SMS)

### Phase 3 : Frontend seller.lorelei.com
- Landing page avec React/Inertia
- Formulaires d'inscription en étapes
- Upload de documents avec prévisualisation
- Dashboard marchand basique

### Phase 4 : Intégration et validation
- Système de validation admin (Filament)
- Workflow d'approbation des marchands
- Gestion des abonnements et facturation
- Tests et déploiement

## Commandes utiles

```bash
# Exécuter les migrations
php artisan migrate

# Exécuter le seeder de test
php artisan db:seed --class=MarchandSellerPlatformSeeder

# Créer un marchand de test
php artisan tinker
>>> App\Models\MarchandAbonnement::creerAbonnement(1, 'premium')

# Vérifier les documents requis
>>> $marchand = App\Models\Marchand::find(1)
>>> $marchand->getDocumentsRequis()
```
