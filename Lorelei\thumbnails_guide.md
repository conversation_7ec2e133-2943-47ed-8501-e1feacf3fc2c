# Guide du système de miniatures pour Loʁelei Marketplace

Ce guide explique comment utiliser le système de miniatures automatiques intégré dans Loʁelei Marketplace.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Structure des dossiers](#structure-des-dossiers)
3. [Tailles de miniatures](#tailles-de-miniatures)
4. [Utilisation dans Filament](#utilisation-dans-filament)
5. [Utilisation dans le frontend](#utilisation-dans-le-frontend)
6. [Commandes Artisan](#commandes-artisan)
7. [Fonctions helper](#fonctions-helper)

## Vue d'ensemble

Le système de miniatures génère automatiquement des versions redimensionnées de vos images lors de l'upload. Ces miniatures sont stockées dans un dossier `thumbnail` séparé avec une structure organisée par type de contenu et taille.

### Avantages

- **Performance** : Chargement plus rapide des pages avec des images optimisées
- **Automatique** : Génération automatique lors de l'upload
- **Flexible** : Différentes tailles selon le contexte d'utilisation
- **Organisé** : Structure de dossiers claire et logique

## Structure des dossiers

```
public/images/
├── products/
│   └── 1/
│       └── abc123.jpg (image originale)
├── categories/
│   └── 2/
│       └── def456.jpg (image originale)
├── banners/
│   └── 3/
│       └── ghi789.jpg (image originale)
├── reviews/
│   └── 4/
│       └── jkl012.jpg (image originale)
└── thumbnail/
    ├── products/
    │   └── 1/
    │       ├── small/
    │       │   └── abc123.jpg (150x150)
    │       ├── medium/
    │       │   └── abc123.jpg (300x300)
    │       └── large/
    │           └── abc123.jpg (600x600)
    ├── categories/
    │   └── 2/
    │       ├── small/
    │       │   └── def456.jpg (100x100)
    │       ├── medium/
    │       │   └── def456.jpg (200x200)
    │       └── large/
    │           └── def456.jpg (400x400)
    ├── banners/
    │   └── 3/
    │       ├── small/
    │       │   └── ghi789.jpg (300x128)
    │       ├── medium/
    │       │   └── ghi789.jpg (600x257)
    │       └── large/
    │           └── ghi789.jpg (1200x514)
    └── reviews/
        └── 4/
            ├── small/
            │   └── jkl012.jpg (100x100)
            ├── medium/
            │   └── jkl012.jpg (200x200)
            └── large/
                └── jkl012.jpg (400x400)
```

## Tailles de miniatures

### Produits
- **Small** : 150x150px - Pour les cartes produits compactes
- **Medium** : 300x300px - Pour les cartes produits standards
- **Large** : 600x600px - Pour les pages de détail produit

### Catégories
- **Small** : 100x100px - Pour les menus et listes compactes
- **Medium** : 200x200px - Pour les cartes catégories
- **Large** : 400x400px - Pour les pages de catégories

### Bannières
- **Small** : 300x128px - Pour les bannières mobiles
- **Medium** : 600x257px - Pour les bannières tablettes
- **Large** : 1200x514px - Pour les bannières desktop

### Reviews
- **Small** : 100x100px - Pour les aperçus de reviews
- **Medium** : 200x200px - Pour l'affichage standard
- **Large** : 400x400px - Pour l'affichage détaillé

## Utilisation dans Filament

Le système de miniatures est automatiquement intégré dans le trait `HandlesImageStorage`. Voici comment l'utiliser :

### Configuration d'un upload d'image

```php
// Dans votre ressource Filament
use App\Filament\Traits\HandlesImageStorage;

class ProduitResource extends Resource
{
    use HandlesImageStorage;

    public static function form(Form $form): Form
    {
        return $form->schema([
            // Autres champs...
            
            self::configureImageUpload(
                FileUpload::make('images')
                    ->label('Images du produit')
                    ->multiple()
                    ->maxFiles(5),
                'products' // Type de contenu
            ),
        ]);
    }

    // Après la création d'un produit
    protected function afterCreate(): void
    {
        self::moveImagesAfterCreate($this->record, 'products', 'images');
    }
}
```

### Types de contenu supportés

- `'products'` - Pour les images de produits
- `'categories'` - Pour les images de catégories
- `'banners'` - Pour les images de bannières
- `'reviews'` - Pour les images de reviews

## Utilisation dans le frontend

### Avec les fonctions helper

```php
// Dans vos contrôleurs ou vues Blade
$productImage = 'products/1/abc123.jpg';

// Obtenir différentes tailles
$smallThumbnail = product_thumbnail($productImage, 'small');
$mediumThumbnail = product_thumbnail($productImage, 'medium');
$largeThumbnail = product_thumbnail($productImage, 'large');

// Ou utiliser la fonction générique
$thumbnail = thumbnail_url($productImage, 'medium');
```

### Dans les vues Blade

```blade
{{-- Affichage d'une carte produit --}}
<div class="product-card">
    <img src="{{ product_thumbnail($product->main_image, 'medium') }}" 
         alt="{{ $product->name }}" 
         class="w-full h-48 object-cover">
</div>

{{-- Affichage d'une bannière responsive --}}
<picture>
    <source media="(max-width: 640px)" srcset="{{ banner_thumbnail($banner->image, 'small') }}">
    <source media="(max-width: 1024px)" srcset="{{ banner_thumbnail($banner->image, 'medium') }}">
    <img src="{{ banner_thumbnail($banner->image, 'large') }}" alt="{{ $banner->title }}">
</picture>

{{-- Galerie d'images de reviews --}}
@foreach($review->images as $image)
    <img src="{{ review_thumbnail($image, 'small') }}" 
         alt="Review image" 
         class="w-16 h-16 object-cover rounded">
@endforeach
```

### Dans React/Vue (via API)

```javascript
// Les URLs de miniatures sont automatiquement incluses dans les réponses API
const ProductCard = ({ product }) => {
    return (
        <div className="product-card">
            <img 
                src={product.thumbnail_urls.medium} 
                alt={product.name}
                className="w-full h-48 object-cover"
            />
        </div>
    );
};
```

## Commandes Artisan

### Générer les miniatures pour les images existantes

```bash
# Générer toutes les miniatures
php artisan thumbnails:generate

# Générer uniquement pour les produits
php artisan thumbnails:generate --type=products

# Régénérer toutes les miniatures (écraser les existantes)
php artisan thumbnails:generate --force

# Générer pour un type spécifique avec force
php artisan thumbnails:generate --type=categories --force
```

### Types disponibles
- `products`
- `categories`
- `banners`
- `reviews`
- `all` (par défaut)

## Fonctions helper

### Fonctions globales

```php
// Fonction générique
thumbnail_url($imagePath, $size = 'medium')

// Fonctions spécifiques par type
product_thumbnail($imagePath, $size = 'medium')
category_thumbnail($imagePath, $size = 'medium')
banner_thumbnail($imagePath, $size = 'medium')
review_thumbnail($imagePath, $size = 'medium')
```

### Classe ThumbnailHelper

```php
use App\Helpers\ThumbnailHelper;

// Obtenir l'URL d'une miniature
$url = ThumbnailHelper::getThumbnailUrl('products/1/abc123.jpg', 'medium');

// Vérifier si une miniature existe
$exists = ThumbnailHelper::thumbnailExists('products/1/abc123.jpg', 'large');

// Obtenir toutes les URLs de miniatures
$allUrls = ThumbnailHelper::getAllThumbnailUrls('products/1/abc123.jpg');
// Retourne: ['original' => '...', 'small' => '...', 'medium' => '...', 'large' => '...']

// Obtenir les tailles disponibles pour un type
$sizes = ThumbnailHelper::getAvailableSizes('products');
// Retourne: ['small', 'medium', 'large']

// Obtenir les dimensions d'une taille
$dimensions = ThumbnailHelper::getSizeDimensions('products', 'medium');
// Retourne: [300, 300]
```

## Intégration avec le CDN

Le système de miniatures est compatible avec le système CDN. Les miniatures seront automatiquement servies depuis le CDN lorsqu'il est activé.

```php
// Les fonctions helper utilisent automatiquement le CDN si configuré
$thumbnailUrl = product_thumbnail($imagePath, 'medium');
// Retourne: https://cdn.lorelei.com/thumbnail/products/1/medium/abc123.jpg
```

## Bonnes pratiques

1. **Utilisez la bonne taille** : Choisissez la taille de miniature appropriée au contexte d'affichage
2. **Fallback automatique** : Si une miniature n'existe pas, l'image originale est automatiquement utilisée
3. **Génération en arrière-plan** : Utilisez la commande `thumbnails:generate` pour traiter les images existantes
4. **Qualité optimisée** : Les miniatures sont sauvegardées avec une qualité de 85% pour un bon équilibre taille/qualité

## Dépannage

### Les miniatures ne se génèrent pas
1. Vérifiez que la librairie Intervention Image est installée
2. Vérifiez les permissions du dossier `public/images/thumbnail`
3. Consultez les logs Laravel pour les erreurs

### Miniatures de mauvaise qualité
1. Ajustez la qualité dans le trait `HandlesImageStorage` (ligne avec `$img->save($thumbnailPath, 85)`)
2. Vérifiez que les images sources sont de bonne qualité

### Performance
1. Utilisez la commande `thumbnails:generate` en arrière-plan pour les gros volumes
2. Considérez l'utilisation d'un CDN pour servir les miniatures
3. Implémentez un système de cache pour les URLs de miniatures fréquemment utilisées
