<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class CheckImportDirectories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:check-directories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vérifie et crée les répertoires nécessaires pour l\'importation de fichiers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $directories = [
            storage_path('app/livewire-tmp'),
            storage_path('app/imports'),
            storage_path('app/public'),
            storage_path('app/temp/imports'),
        ];

        foreach ($directories as $directory) {
            if (!File::exists($directory)) {
                File::makeDirectory($directory, 0755, true);
                $this->info("Répertoire créé: {$directory}");
            } else {
                // Vérifier les permissions
                if (!is_writable($directory)) {
                    chmod($directory, 0755);
                    $this->info("Permissions mises à jour pour: {$directory}");
                } else {
                    $this->info("Répertoire existant avec bonnes permissions: {$directory}");
                }
            }
        }

        // Créer un fichier .gitignore dans chaque répertoire pour éviter de committer les fichiers temporaires
        foreach ($directories as $directory) {
            $gitignorePath = $directory . '/.gitignore';
            if (!File::exists($gitignorePath)) {
                File::put($gitignorePath, "*\n!.gitignore\n");
                $this->info("Fichier .gitignore créé dans: {$directory}");
            }
        }

        $this->info('Tous les répertoires nécessaires ont été vérifiés et créés si nécessaire.');
    }
}
