<?php

namespace App\Filament\Marchand\Resources;

use App\Filament\Marchand\Resources\CategorieResource\Pages;
use App\Models\Categorie;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CategorieResource extends Resource
{
    protected static ?string $model = Categorie::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    protected static ?string $navigationGroup = 'Catalogue';

    protected static ?int $navigationSort = 2;

    protected static ?string $recordTitleAttribute = 'nom';

    protected static ?string $recordRouteKeyName = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations de la catégorie')
                            ->schema([
                                Forms\Components\TextInput::make('nom')
                                    ->label('Nom')
                                    ->required()
                                    ->maxLength(255)
                                    ->disabled(),

                                Forms\Components\TextInput::make('slug')
                                    ->label('Slug')
                                    ->required()
                                    ->maxLength(255)
                                    ->disabled(),

                                Forms\Components\Select::make('categorie_parent_id')
                                    ->label('Catégorie parente')
                                    ->relationship('categorieParent', 'nom')
                                    ->searchable()
                                    ->preload()
                                    ->disabled(),

                                Forms\Components\TextInput::make('niveau')
                                    ->label('Niveau')
                                    ->numeric()
                                    ->disabled(),

                                Forms\Components\TextInput::make('category_path')
                                    ->label('Chemin de catégorie')
                                    ->disabled(),

                                Forms\Components\Textarea::make('description')
                                    ->label('Description')
                                    ->columnSpanFull()
                                    ->disabled(),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Image')
                            ->schema([
                                Forms\Components\FileUpload::make('image_url')
                                    ->label('Image')
                                    ->image()
                                    ->disk('public_images')
                                    ->disabled(),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image_url')
                    ->label('Image')
                    ->disk('public_images')
                    ->circular(),

                Tables\Columns\TextColumn::make('nom')
                    ->label('Nom')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->label('Slug')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('categorieParent.nom')
                    ->label('Catégorie parente')
                    ->searchable(),

                Tables\Columns\TextColumn::make('niveau')
                    ->label('Niveau')
                    ->sortable(),

                Tables\Columns\TextColumn::make('category_path')
                    ->label('Chemin')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('produits_count')
                    ->label('Produits')
                    ->counts('produits')
                    ->sortable(),

                Tables\Columns\TextColumn::make('sous_categories_count')
                    ->label('Sous-catégories')
                    ->counts('sousCategories')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('categorie_parent_id')
                    ->label('Catégorie parente')
                    ->relationship('categorieParent', 'nom'),

                Tables\Filters\Filter::make('root_categories')
                    ->label('Catégories racines')
                    ->query(fn (Builder $query): Builder => $query->whereNull('categorie_parent_id')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCategories::route('/'),
            'view' => Pages\ViewCategorie::route('/{record}'),
        ];
    }
}
