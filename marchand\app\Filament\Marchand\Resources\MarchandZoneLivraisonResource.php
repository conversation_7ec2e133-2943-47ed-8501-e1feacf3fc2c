<?php

namespace App\Filament\Marchand\Resources;

use App\Filament\Marchand\Resources\MarchandZoneLivraisonResource\Pages;
use App\Models\MarchandZoneLivraison;
use App\Models\ZoneLivraison;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class MarchandZoneLivraisonResource extends Resource
{
    protected static ?string $model = MarchandZoneLivraison::class;

    protected static ?string $navigationIcon = 'heroicon-o-map-pin';

    protected static ?string $navigationGroup = 'Livraison';

    protected static ?string $navigationLabel = 'Mes zones de livraison';

    protected static ?int $navigationSort = 1;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('marchand_id', Auth::id());
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('marchand_id')
                    ->default(Auth::id()),

                Forms\Components\Select::make('zone_livraison_id')
                    ->label('Zone de livraison')
                    ->options(function () {
                        // Récupérer toutes les zones actives
                        return ZoneLivraison::where('actif', true)
                            ->orderBy('type')
                            ->orderBy('nom')
                            ->get()
                            ->mapWithKeys(function ($zone) {
                                $prefix = match ($zone->type) {
                                    'Pays' => '🌍 ',
                                    'Region' => '🏞️ ',
                                    'Ville' => '🏙️ ',
                                    'Quartier' => '🏘️ ',
                                    default => '',
                                };

                                return [$zone->id => $prefix . $zone->nom . ' (' . $zone->type . ')'];
                            });
                    })
                    ->searchable()
                    ->preload()
                    ->required()
                    ->helperText('Sélectionnez la zone géographique où vous pouvez livrer'),

                Forms\Components\TextInput::make('frais_livraison')
                    ->label('Frais de livraison (FCFA)')
                    ->required()
                    ->numeric()
                    ->minValue(0)
                    ->step(100)
                    ->helperText('Montant des frais de livraison pour cette zone'),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('delai_livraison_min')
                            ->label('Délai minimum (jours)')
                            ->required()
                            ->integer()
                            ->minValue(0)
                            ->helperText('Délai minimum de livraison en jours'),

                        Forms\Components\TextInput::make('delai_livraison_max')
                            ->label('Délai maximum (jours)')
                            ->required()
                            ->integer()
                            ->minValue(function (callable $get) {
                                return $get('delai_livraison_min');
                            })
                            ->helperText('Délai maximum de livraison en jours'),
                    ]),

                Forms\Components\Toggle::make('actif')
                    ->label('Actif')
                    ->helperText('Activez ou désactivez cette zone de livraison')
                    ->default(true)
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('zoneLivraison.nom')
                    ->label('Zone de livraison')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('zoneLivraison.type')
                    ->label('Type de zone')
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Pays' => 'success',
                        'Region' => 'info',
                        'Ville' => 'warning',
                        'Quartier' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('frais_livraison')
                    ->label('Frais de livraison')
                    ->money('XAF')
                    ->sortable(),

                Tables\Columns\TextColumn::make('delai_livraison')
                    ->label('Délai de livraison')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderBy('delai_livraison_min', $direction);
                    }),

                Tables\Columns\IconColumn::make('actif')
                    ->label('Actif')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Mis à jour le')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('zone_type')
                    ->label('Type de zone')
                    ->options([
                        'Pays' => 'Pays',
                        'Region' => 'Région',
                        'Ville' => 'Ville',
                        'Quartier' => 'Quartier',
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query->when(
                            $data['value'],
                            fn (Builder $query, $value) => $query->whereHas('zoneLivraison', fn ($q) => $q->where('type', $value))
                        );
                    }),

                Tables\Filters\TernaryFilter::make('actif')
                    ->label('Actif')
                    ->boolean(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Modifier'),

                Tables\Actions\DeleteAction::make()
                    ->label('Supprimer')
                    ->requiresConfirmation()
                    ->modalDescription('Êtes-vous sûr de vouloir supprimer cette zone de livraison ? Cette action est irréversible.'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Supprimer la sélection')
                        ->requiresConfirmation()
                        ->modalDescription('Êtes-vous sûr de vouloir supprimer ces zones de livraison ? Cette action est irréversible.'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMarchandZoneLivraisons::route('/'),
            'create' => Pages\CreateMarchandZoneLivraison::route('/create'),
            'edit' => Pages\EditMarchandZoneLivraison::route('/{record}/edit'),
        ];
    }
}
