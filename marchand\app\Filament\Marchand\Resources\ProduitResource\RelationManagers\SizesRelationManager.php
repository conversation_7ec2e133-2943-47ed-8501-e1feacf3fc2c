<?php

namespace App\Filament\Marchand\Resources\ProduitResource\RelationManagers;

use App\Models\Size;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class SizesRelationManager extends RelationManager
{
    protected static string $relationship = 'sizes';

    protected static ?string $recordTitleAttribute = 'code';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('size_id')
                    ->label('Taille (Size)')
                    ->options(Size::where('is_active', true)->get()->pluck('full_name', 'id'))
                    ->searchable()
                    ->preload()
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->label('Code')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('name_fr')
                    ->label('Nom (français)')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('name_en')
                    ->label('Nom (anglais)')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('category')
                    ->label('Catégorie')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'clothing' => 'Vêtements (Clothing)',
                        'shoes' => 'Chaussures (Shoes)',
                        'accessories' => 'Accessoires (Accessories)',
                        'other' => 'Autre (Other)',
                        default => $state,
                    })
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label('Catégorie')
                    ->options([
                        'clothing' => 'Vêtements (Clothing)',
                        'shoes' => 'Chaussures (Shoes)',
                        'accessories' => 'Accessoires (Accessories)',
                        'other' => 'Autre (Other)',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->preloadRecordSelect()
                    ->label('Ajouter des tailles')
                    ->modalHeading('Ajouter des tailles au produit')
                    ->modalDescription('Sélectionnez les tailles disponibles pour ce produit')
                    ->multiple(),
            ])
            ->actions([
                Tables\Actions\DetachAction::make()
                    ->label('Retirer'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make()
                        ->label('Retirer la sélection'),
                ]),
            ]);
    }
}
