<?php

namespace App\Filament\Marchand\Resources;

use App\Filament\Marchand\Resources\ProfileResource\Pages;
use App\Models\Marchand;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Hash;

class ProfileResource extends Resource
{
    protected static ?string $model = Marchand::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';
    
    protected static ?string $navigationLabel = 'Mon Profil';
    
    protected static ?int $navigationSort = 10;
    
    protected static ?string $slug = 'mon-profil';
    
    protected static bool $shouldRegisterNavigation = true;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations de l\'entreprise')
                            ->schema([
                                Forms\Components\TextInput::make('nomEntreprise')
                                    ->label('Nom de l\'entreprise')
                                    ->required()
                                    ->maxLength(255),
                                
                                Forms\Components\TextInput::make('idFiscal')
                                    ->label('Numéro d\'identification fiscale')
                                    ->maxLength(50),
                            ]),
                        
                        Forms\Components\Section::make('Informations bancaires')
                            ->schema([
                                Forms\Components\TextInput::make('banqueNom')
                                    ->label('Nom de la banque')
                                    ->maxLength(100),
                                
                                Forms\Components\TextInput::make('banqueNumeroCompte')
                                    ->label('Numéro de compte')
                                    ->maxLength(50),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),
                
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Compte utilisateur')
                            ->schema([
                                Forms\Components\TextInput::make('user.email')
                                    ->label('Email')
                                    ->email()
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true, table: 'users', column: 'email')
                                    ->disabled(),
                                
                                Forms\Components\TextInput::make('password')
                                    ->label('Nouveau mot de passe')
                                    ->password()
                                    ->dehydrated(fn (?string $state): bool => filled($state))
                                    ->dehydrateStateUsing(fn (string $state): string => Hash::make($state))
                                    ->live(debounce: 500)
                                    ->same('passwordConfirmation'),
                                
                                Forms\Components\TextInput::make('passwordConfirmation')
                                    ->label('Confirmer le mot de passe')
                                    ->password()
                                    ->dehydrated(false),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nomEntreprise')
                    ->label('Nom de l\'entreprise'),
                
                Tables\Columns\TextColumn::make('user.email')
                    ->label('Email'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\EditProfile::route('/'),
        ];
    }
    
    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        // Ne montrer que le profil du marchand connecté
        return parent::getEloquentQuery()
            ->where('user_id', auth()->id());
    }
}
