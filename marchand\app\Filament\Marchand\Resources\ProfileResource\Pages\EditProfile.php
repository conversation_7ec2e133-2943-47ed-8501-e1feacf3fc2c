<?php

namespace App\Filament\Marchand\Resources\ProfileResource\Pages;

use App\Filament\Marchand\Resources\ProfileResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Support\Htmlable;

class EditProfile extends EditRecord
{
    protected static string $resource = ProfileResource::class;

    public function mount($record = null): void
    {
        // Récupérer l'ID du marchand connecté
        $marchandId = auth()->user()->marchands->first()->id ?? null;

        if (!$marchandId) {
            abort(404);
        }

        // Appeler le mount parent avec l'ID du marchand
        parent::mount($marchandId);
    }

    public function getTitle(): string|Htmlable
    {
        return 'Mon profil';
    }

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Profil mis à jour')
            ->body('Votre profil a été mis à jour avec succès.');
    }
}
