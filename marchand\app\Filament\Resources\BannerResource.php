<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BannerResource\Pages;
use App\Filament\Traits\HandlesImageStorage;
use App\Models\Banner;
use Filament\Forms;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class BannerResource extends Resource
{
    use HandlesImageStorage;
    protected static ?string $model = Banner::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationGroup = 'Marketing';

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'position';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations de la bannière')
                            ->schema([
                                Forms\Components\Select::make('position')
                                    ->label('Position')
                                    ->options([
                                        'carousel' => 'Carousel principal (page d\'accueil)',
                                        'promotional' => 'Bannières promotionnelles (page d\'accueil)',
                                        'category_header' => 'En-tête de catégorie',
                                        'product_sidebar' => 'Barre latérale des produits',
                                        'checkout' => 'Page de paiement',
                                        'footer' => 'Pied de page',
                                    ])
                                    ->required()
                                    ->searchable()
                                    ->allowHtml()
                                    ->createOptionForm([
                                        Forms\Components\TextInput::make('custom_position')
                                            ->label('Position personnalisée')
                                            ->required()
                                            ->maxLength(100)
                                            ->helperText('Entrez un identifiant unique pour cette position (ex: promo_ete_2023)')
                                    ])
                                    ->createOptionUsing(function (array $data) {
                                        return [
                                            'value' => $data['custom_position'],
                                            'label' => '<em>Personnalisée:</em> ' . $data['custom_position'],
                                        ];
                                    })
                                    ->helperText('Sélectionnez l\'emplacement où la bannière sera affichée ou créez une position personnalisée'),

                                Tabs::make('Traductions')
                                    ->tabs([
                                        Tabs\Tab::make('Français')
                                            ->icon('heroicon-m-flag')
                                            ->schema([
                                                Forms\Components\TextInput::make('title.fr')
                                                    ->label('Titre (FR)')
                                                    ->maxLength(255),

                                                Forms\Components\Textarea::make('description.fr')
                                                    ->label('Description (FR)')
                                                    ->rows(3),

                                                Forms\Components\TextInput::make('button_text.fr')
                                                    ->label('Texte du bouton (FR)')
                                                    ->maxLength(100),
                                            ]),

                                        Tabs\Tab::make('English')
                                            ->icon('heroicon-m-flag')
                                            ->schema([
                                                Forms\Components\TextInput::make('title.en')
                                                    ->label('Titre (EN)')
                                                    ->maxLength(255),

                                                Forms\Components\Textarea::make('description.en')
                                                    ->label('Description (EN)')
                                                    ->rows(3),

                                                Forms\Components\TextInput::make('button_text.en')
                                                    ->label('Texte du bouton (EN)')
                                                    ->maxLength(100),
                                            ]),
                                    ])
                                    ->columnSpanFull(),

                                Forms\Components\Select::make('type')
                                    ->label('Type de bannière')
                                    ->options([
                                        // Types génériques
                                        'primary' => 'Principale (bleu-indigo)',
                                        'secondary' => 'Secondaire (violet-indigo)',
                                        'promotional' => 'Promotionnelle (ambre-rose)',

                                        // Types spécifiques pour le carousel
                                        'carousel-blue' => 'Carousel - Bleu indigo',
                                        'carousel-amber' => 'Carousel - Ambre orange',
                                        'carousel-emerald' => 'Carousel - Émeraude',

                                        // Types spécifiques pour les bannières promotionnelles
                                        'promo-purple' => 'Promo - Violet indigo',
                                        'promo-amber' => 'Promo - Ambre rose',
                                    ])
                                    ->searchable()
                                    ->helperText('Détermine le style et les couleurs de la bannière'),

                                Forms\Components\TextInput::make('target_url')
                                    ->label('URL de destination')
                                    ->url()
                                    ->maxLength(255),

                                Forms\Components\DateTimePicker::make('start_date')
                                    ->label('Date de début'),

                                Forms\Components\DateTimePicker::make('end_date')
                                    ->label('Date de fin'),

                                Forms\Components\TextInput::make('priorite')
                                    ->label('Priorité')
                                    ->numeric()
                                    ->default(0)
                                    ->helperText('Plus la valeur est élevée, plus la bannière sera prioritaire'),

                                Forms\Components\Toggle::make('is_active')
                                    ->label('Actif')
                                    ->default(true),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Image')
                            ->schema([
                                self::configureImageUpload(
                                    Forms\Components\FileUpload::make('image_url')
                                        ->label('Image')
                                        ->image()
                                        ->imageEditor()
                                        ->helperText('Optionnel - Si non fournie, la bannière utilisera un dégradé de couleur'),
                                    'banners'
                                ),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image_url')
                    ->label('Image')
                    ->disk('public_images'),

                Tables\Columns\TextColumn::make('position')
                    ->label('Position')
                    ->searchable(),

                Tables\Columns\TextColumn::make('title')
                    ->label('Titre')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state)) {
                            return $state['fr'] ?? '';
                        } elseif (is_string($state)) {
                            try {
                                $decoded = json_decode($state, true);
                                if (is_array($decoded)) {
                                    return $decoded['fr'] ?? '';
                                }
                            } catch (\Exception $e) {
                                // Si le décodage échoue, retourner l'état tel quel
                            }
                        }
                        return $state;
                    })
                    ->limit(30)
                    ->searchable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match (true) {
                        str_starts_with($state, 'primary') => 'primary',
                        str_starts_with($state, 'carousel-blue') => 'primary',

                        str_starts_with($state, 'secondary') => 'secondary',
                        str_starts_with($state, 'promo-purple') => 'secondary',

                        str_starts_with($state, 'promotional') => 'success',
                        str_starts_with($state, 'promo-amber') => 'warning',

                        str_starts_with($state, 'carousel-amber') => 'warning',
                        str_starts_with($state, 'carousel-emerald') => 'success',

                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('target_url')
                    ->label('URL de destination')
                    ->limit(30),

                Tables\Columns\TextColumn::make('start_date')
                    ->label('Début')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('end_date')
                    ->label('Fin')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('priorite')
                    ->label('Priorité')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Actif')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\Filter::make('active')
                    ->label('Bannières actives')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true)),

                Tables\Filters\Filter::make('current')
                    ->label('Bannières en cours')
                    ->query(function (Builder $query): Builder {
                        return $query
                            ->where('is_active', true)
                            ->where(function (Builder $query) {
                                $query->whereNull('start_date')
                                    ->orWhere('start_date', '<=', now());
                            })
                            ->where(function (Builder $query) {
                                $query->whereNull('end_date')
                                    ->orWhere('end_date', '>=', now());
                            });
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activer')
                        ->icon('heroicon-o-check-circle')
                        ->action(fn (array $records) => Banner::whereIn('id', $records)->update(['is_active' => true])),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Désactiver')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(fn (array $records) => Banner::whereIn('id', $records)->update(['is_active' => false])),
                ]),
            ])
            ->defaultSort('priorite', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBanners::route('/'),
            'create' => Pages\CreateBanner::route('/create'),
            'edit' => Pages\EditBanner::route('/{record}/edit'),
        ];
    }
}
