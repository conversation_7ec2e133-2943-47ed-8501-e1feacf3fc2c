<?php

namespace App\Filament\Resources\ProduitResource\Pages;

use App\Filament\Resources\ProduitResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Session;

class EditProduit extends EditRecord
{
    protected static string $resource = ProduitResource::class;

    public function mount($record): void
    {
        parent::mount($record);

        // Vérifier si on vient de créer un produit
        if (Session::has('product_created')) {
            Session::forget('product_created');

            // Afficher un modal pour informer l'utilisateur qu'il peut ajouter des variantes
            $this->showVariantInfoModal();
        }
    }

    protected function showVariantInfoModal(): void
    {
        Notification::make()
            ->title('Ajouter des variantes')
            ->body('Vous pouvez maintenant ajouter des variantes à votre produit en utilisant la section "Variantes" ci-dessous. Les variantes permettent de proposer différentes options comme des couleurs, tailles, etc.')
            ->info()
            ->persistent()
            ->actions([
                \Filament\Notifications\Actions\Action::make('view_variants')
                    ->label('Voir les variantes')
                    ->button()
                    ->close()
            ])
            ->send();
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Mettre à jour la date de mise à jour
        $data['misAJourLe'] = now();

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
