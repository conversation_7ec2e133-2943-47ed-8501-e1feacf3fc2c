<?php

namespace App\Filament\Resources\ProduitResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class VariantsRelationManager extends RelationManager
{
    protected static string $relationship = 'variants';

    protected static ?string $recordTitleAttribute = 'sku';

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Vérifier si les attributs contiennent des couleurs et s'assurer que le code est défini
        if (isset($data['attributs']) && is_array($data['attributs'])) {
            // Vérifier les doublons d'attributs
            $attributeTypes = [];
            $attributeValues = [];
            $hasError = false;

            foreach ($data['attributs'] as $key => $attribut) {
                if (!isset($attribut['type'])) {
                    continue;
                }

                $type = $attribut['type'];

                // Vérifier si ce type d'attribut existe déjà
                if (in_array($type, $attributeTypes)) {
                    // Erreur: doublon de type d'attribut
                    \Filament\Notifications\Notification::make()
                        ->title('Erreur: Doublon d\'attribut')
                        ->body('Vous ne pouvez pas avoir plusieurs attributs du même type (' . $type . '). Veuillez créer une variante distincte pour chaque combinaison d\'attributs.')
                        ->danger()
                        ->send();
                    $hasError = true;
                    break;
                }

                $attributeTypes[] = $type;

                // Pour les attributs de type couleur
                if ($type === 'couleur') {
                    // Toujours définir une valeur par défaut pour le code couleur
                    $data['attributs'][$key]['code'] = !empty($attribut['code']) ? $attribut['code'] : '#e80a0a';
                }

                // Vérifier les doublons de valeurs pour le même type
                if (isset($attribut['valeur'])) {
                    $valueKey = $type . '-' . $attribut['valeur'];
                    if (in_array($valueKey, $attributeValues)) {
                        // Erreur: doublon de valeur pour ce type
                        \Filament\Notifications\Notification::make()
                            ->title('Erreur: Valeur en double')
                            ->body('Vous avez spécifié la même valeur "' . $attribut['valeur'] . '" plusieurs fois pour le type "' . $type . '".')
                            ->danger()
                            ->send();
                        $hasError = true;
                        break;
                    }
                    $attributeValues[] = $valueKey;
                }
            }

            // Si des erreurs ont été détectées, retourner une erreur
            if ($hasError) {
                throw new \Exception('Erreur de validation des attributs. Veuillez corriger les problèmes mentionnés.');
            }
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Appliquer la même logique lors de la sauvegarde
        return $this->mutateFormDataBeforeCreate($data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('sku')
                    ->label('SKU')
                    ->helperText('Code unique pour cette variante (optionnel)')
                    ->maxLength(255),

                Forms\Components\TextInput::make('prix_supplement')
                    ->label('Supplément de prix')
                    ->numeric()
                    ->default(0)
                    ->prefix(fn(Forms\Get $get) => $get('../../currency') ?: 'FCFA')
                    ->helperText('Montant à ajouter au prix de base du produit'),

                Forms\Components\TextInput::make('stock')
                    ->label('Stock disponible')
                    ->numeric()
                    ->default(0)
                    ->required(),

                Forms\Components\Repeater::make('attributs')
                    ->label('Attributs du produit')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->label('Type d\'attribut (Attribute type)')
                            ->options([
                                'couleur' => 'Couleur (Color)',
                                'taille' => 'Taille (Size)',
                                'matiere' => 'Matière (Material)',
                                'autre' => 'Autre (Other)',
                            ])
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function (Forms\Set $set, $state) {
                                $set('valeur', null);

                                // Si le type est couleur, définir automatiquement le code couleur par défaut
                                if ($state === 'couleur') {
                                    $set('code', '#e80a0a');

                                    // Afficher une notification pour confirmer
                                    \Filament\Notifications\Notification::make()
                                        ->title('Type couleur sélectionné')
                                        ->body('Code couleur par défaut défini: #e80a0a')
                                        ->success()
                                        ->send();
                                }
                            }),

                        Forms\Components\Grid::make()
                            ->schema(function (Forms\Get $get) {
                                $type = $get('type');

                                if ($type === 'couleur') {
                                    return [
                                        Forms\Components\TextInput::make('nom')
                                            ->label('Nom de la couleur (Color name)')
                                            ->placeholder('Ex: Rouge, Bleu, Vert')
                                            ->required(),

                                        Forms\Components\ColorPicker::make('code')
                                            ->label('Code couleur (Color code)')
                                            ->required()
                                            ->default('#e80a0a')
                                            ->live(),

                                        Forms\Components\Toggle::make('with_image')
                                            ->label('Utiliser une image pour cette couleur')
                                            ->helperText('Si activé, une image sera utilisée à la place de la couleur dans le sélecteur')
                                            ->default(false)
                                            ->reactive(),

                                        Forms\Components\FileUpload::make('color_image')
                                            ->label('Image de la couleur')
                                            ->helperText('Image qui représente cette couleur (texture, motif, etc.)')
                                            ->image()
                                            ->imageEditor()
                                            ->disk('public_images')
                                            ->directory('products/variants')
                                            ->visibility('public')
                                            ->hidden(fn(Forms\Get $get) => !$get('with_image'))
                                            ->saveUploadedFileUsing(function ($file) {
                                                // Récupérer le produit parent
                                                $produitRecord = $this->getOwnerRecord();

                                                if (!$produitRecord || !$produitRecord->exists) {
                                                    // Si le produit n'existe pas encore, stocker temporairement dans le dossier '0'
                                                    return $file->store("products/variants/0/colors", 'public_images');
                                                }

                                                // Déterminer le dossier basé sur l'ID du produit
                                                $produitId = $produitRecord->id;
                                                $folderPrefix = \App\Helpers\ImageStorage::getFolderPrefix($produitId);
                                                $path = "products/{$folderPrefix}/colors";

                                                // Créer le dossier s'il n'existe pas
                                                $fullPath = public_path("images/{$path}");
                                                if (!file_exists($fullPath)) {
                                                    mkdir($fullPath, 0755, true);
                                                }

                                                // Générer un nom de fichier unique et stocker le fichier
                                                $filename = $file->hashName();
                                                $file->storeAs($path, $filename, 'public_images');

                                                return "{$path}/{$filename}";
                                            }),
                                    ];
                                }

                                $type = $get('type');

                                if ($type === 'taille') {
                                    return [
                                        Forms\Components\Select::make('category')
                                            ->label('Catégorie de taille (Size category)')
                                            ->options(function () {
                                                return \App\Models\Size::getDistinctCategories();
                                            })
                                            ->required()
                                            ->reactive()
                                            ->afterStateUpdated(fn(Forms\Set $set) => $set('valeur', null)),

                                        Forms\Components\Select::make('valeur')
                                            ->label('Taille (Size)')
                                            ->options(function (Forms\Get $get) {
                                                $category = $get('category');

                                                // Récupérer les tailles associées au produit et filtrées par catégorie
                                                $record = $this->getOwnerRecord();
                                                if ($record && $record->sizes && count($record->sizes) > 0 && $category) {
                                                    return $record->sizes()
                                                        ->where('is_active', true)
                                                        ->where('category', $category)
                                                        ->get()
                                                        ->pluck('full_name', 'code')
                                                        ->toArray();
                                                }

                                                // Si aucune taille n'est associée ou pas de catégorie sélectionnée
                                                if ($category) {
                                                    return \App\Models\Size::where('is_active', true)
                                                        ->where('category', $category)
                                                        ->get()
                                                        ->pluck('full_name', 'code')
                                                        ->toArray();
                                                }

                                                return [];
                                            })
                                            ->searchable()
                                            ->preload()
                                            ->required()
                                            ->disabled(fn(Forms\Get $get) => !$get('category'))
                                            ->helperText('Sélectionnez une taille prédéfinie'),

                                        Forms\Components\TextInput::make('quantity')
                                            ->label('Quantité pour cette taille')
                                            ->numeric()
                                            ->minValue(0)
                                            ->default(0)
                                            ->required()
                                            ->reactive()
                                            ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                                // Mettre à jour le stock total du variant
                                                $record = $this->getOwnerRecord();
                                                if ($record) {
                                                    $attributs = $get('../../attributs') ?: [];
                                                    $totalStock = 0;

                                                    foreach ($attributs as $attribut) {
                                                        if (isset($attribut['type']) && $attribut['type'] === 'taille' && isset($attribut['quantity'])) {
                                                            $totalStock += (int)$attribut['quantity'];
                                                        }
                                                    }

                                                    $set('../../stock', $totalStock);
                                                }
                                            })
                                            ->helperText('Quantité de produits disponibles pour cette taille'),
                                    ];
                                }

                                return [
                                    Forms\Components\TextInput::make('valeur')
                                        ->label('Valeur (Value)')
                                        ->placeholder(function (Forms\Get $get) {
                                            $type = $get('type');
                                            return match ($type) {
                                                'matiere' => 'Ex: Coton, Laine, Polyester',
                                                default => 'Ex: Valeur de l\'attribut',
                                            };
                                        })
                                        ->required(),
                                ];
                            })
                            ->columns(2),
                    ])
                    ->itemLabel(function (array $state): ?string {
                        $type = $state['type'] ?? null;

                        if ($type === 'couleur' && isset($state['nom'])) {
                            return ucfirst($type) . ': ' . $state['nom'];
                        }

                        if (isset($state['valeur'])) {
                            return ucfirst($type) . ': ' . $state['valeur'];
                        }

                        return ucfirst($type ?? 'Attribut');
                    })
                    ->collapsible()
                    ->collapsed(false)
                    ->addActionLabel('Ajouter un attribut (Add attribute)')
                    ->reorderable()
                    ->required(),

                Forms\Components\FileUpload::make('images')
                    ->label('Images spécifiques à cette variante')
                    ->helperText('Images qui seront affichées lorsque cette variante est sélectionnée')
                    ->multiple()
                    ->image()
                    ->imageEditor()
                    ->disk('public_images')
                    ->directory('products/variants')
                    ->visibility('public')
                    ->maxFiles(5)
                    ->reorderable()
                    ->saveUploadedFileUsing(function ($file, $record) {
                        if (!$record || !$record->exists) {
                            // Si le record n'existe pas encore, stocker temporairement dans le dossier '0'
                            return $file->store("products/variants/0", 'public_images');
                        }

                        // Déterminer le dossier basé sur l'ID du produit et de la variante
                        $produitId = $record->produit_id;
                        $variantId = $record->id;
                        $folderPrefix = \App\Helpers\ImageStorage::getFolderPrefix($produitId);
                        $path = "products/{$folderPrefix}/variants/{$variantId}";

                        // Créer le dossier s'il n'existe pas
                        $fullPath = public_path("images/{$path}");
                        if (!file_exists($fullPath)) {
                            mkdir($fullPath, 0755, true);
                        }

                        // Générer un nom de fichier unique et stocker le fichier
                        $filename = $file->hashName();
                        $file->storeAs($path, $filename, 'public_images');

                        return "{$path}/{$filename}";
                    }),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('sku')
                    ->label('SKU')
                    ->searchable(),

                Tables\Columns\TextColumn::make('prix_supplement')
                    ->label('Supplément de prix')
                    ->money(fn($record) => $record->produit->currency ?? 'FCFA')
                    ->sortable(),

                Tables\Columns\TextColumn::make('stock')
                    ->label('Stock')
                    ->sortable(),

                Tables\Columns\TextColumn::make('attributs')
                    ->label('Attributs')
                    ->formatStateUsing(function ($state, $record) {
                        // Récupérer directement les attributs depuis le modèle
                        if (isset($record->attributs)) {
                            $state = $record->attributs;
                        }

                        // Afficher le type et la valeur pour le débogage
                        // \Filament\Notifications\Notification::make()
                        //     ->title('Attributs')
                        //     ->body('Type: ' . gettype($state) . ', Valeur: ' . (is_string($state) ? $state : json_encode($state)))
                        //     ->success()
                        //     ->send();

                        // Essayer différentes méthodes pour convertir en tableau
                        if (is_string($state)) {
                            // Méthode 1: json_decode standard
                            $decoded = json_decode($state, true);
                            if (is_array($decoded)) {
                                $state = $decoded;
                            } else {
                                // Méthode 2: Nettoyer la chaîne et réessayer
                                $cleanedState = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $state);
                                $decoded = json_decode($cleanedState, true);
                                if (is_array($decoded)) {
                                    $state = $decoded;
                                }
                            }
                        }

                        // Si ce n'est toujours pas un tableau, vérifier si c'est un objet
                        if (!is_array($state) && is_object($state)) {
                            $state = (array) $state;
                        }

                        // Si ce n'est toujours pas un tableau après toutes ces tentatives, retourner une chaîne vide
                        if (!is_array($state)) {
                            return '<span class="text-gray-400">Aucun attribut</span>';
                        }

                        $attributes = [];
                        foreach ($state as $attribute) {
                            $type = $attribute['type'] ?? 'autre';

                            if ($type === 'couleur') {
                                // Pour les attributs de type couleur
                                if (isset($attribute['nom'])) {
                                    $colorName = $attribute['nom'];
                                    // Vérifier si l'attribut utilise une image
                                    $withImage = $attribute['with_image'] ?? false;
                                    $colorImage = $attribute['color_image'] ?? null;

                                    if ($withImage && $colorImage) {
                                        // Afficher l'image de la couleur
                                        $imageUrl = url('/images/' . $colorImage);
                                        $attributes[] = "<div class='flex items-center gap-1'>
                                            <span class='inline-block w-5 h-5 rounded-full overflow-hidden'>
                                                <img src='{$imageUrl}' alt='{$colorName}' class='w-full h-full object-cover' />
                                            </span>
                                            <span>Couleur: {$colorName} (avec image)</span>
                                        </div>";
                                    } else {
                                        // Utiliser le code si disponible, sinon utiliser color_picker, sinon la valeur par défaut
                                        $colorCode = $attribute['code'] ?? $attribute['color_picker'] ?? '#e80a0a';
                                        $attributes[] = "<div class='flex items-center gap-1'>
                                            <span class='inline-block w-3 h-3 rounded-full' style='background-color: {$colorCode}'></span>
                                            <span>Couleur: {$colorName}</span>
                                        </div>";
                                    }
                                }
                            } elseif ($type === 'taille' && isset($attribute['valeur']) && $attribute['valeur'] !== null) {
                                // Pour les attributs de type taille
                                $quantity = $attribute['quantity'] ?? 0;
                                $category = $attribute['category'] ?? 'N/A';
                                $categoryLabel = match ($category) {
                                    'clothing' => 'Vêtements',
                                    'shoes' => 'Chaussures',
                                    'accessories' => 'Accessoires',
                                    'other' => 'Autre',
                                    default => $category,
                                };
                                $attributes[] = "<div class='flex flex-col'>
                                    <span>Taille: {$attribute['valeur']} ({$categoryLabel})</span>
                                    <span class='text-xs text-gray-500'>Quantité: {$quantity}</span>
                                </div>";
                            } elseif (isset($attribute['valeur']) && $attribute['valeur'] !== null) {
                                // Pour les autres types d'attributs avec une valeur non nulle
                                $attributes[] = ucfirst($type) . ": {$attribute['valeur']}";
                            }
                        }

                        return count($attributes) > 0
                            ? implode('<br>', $attributes)
                            : '<span class="text-gray-400">Aucun attribut</span>';
                    })
                    ->html(),

                Tables\Columns\ImageColumn::make('images')
                    ->label('Images')
                    ->circular()
                    ->disk('public_images')
                    ->stacked()
                    ->limit(3),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
