<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ZoneLivraisonResource\Pages;
use App\Models\ZoneLivraison;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ZoneLivraisonResource extends Resource
{
    protected static ?string $model = ZoneLivraison::class;

    protected static ?string $navigationIcon = 'heroicon-o-map';

    protected static ?string $navigationGroup = 'Livraison';

    protected static ?string $navigationLabel = 'Zones de livraison';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nom')
                    ->required()
                    ->maxLength(255)
                    ->label('Nom'),

                Forms\Components\Select::make('type')
                    ->required()
                    ->options([
                        'Pays' => 'Pays',
                        'Region' => 'Région',
                        'Ville' => 'Ville',
                        'Quartier' => 'Quartier',
                    ])
                    ->label('Type')
                    ->reactive(),

                Forms\Components\Select::make('parent_id')
                    ->relationship('parent', 'nom', function ($query, $get) {
                        $type = $get('type');

                        if ($type === 'Region') {
                            return $query->where('type', 'Pays');
                        } elseif ($type === 'Ville') {
                            return $query->where('type', 'Region');
                        } elseif ($type === 'Quartier') {
                            return $query->where('type', 'Ville');
                        }

                        return $query;
                    })
                    ->searchable()
                    ->preload()
                    ->label('Zone parente')
                    ->required(fn ($get) => $get('type') !== 'Pays')
                    ->visible(fn ($get) => $get('type') !== 'Pays'),

                Forms\Components\TextInput::make('code')
                    ->maxLength(50)
                    ->label('Code')
                    ->helperText('Code unique pour cette zone (ex: CM pour Cameroun)'),

                Forms\Components\Toggle::make('actif')
                    ->required()
                    ->default(true)
                    ->label('Actif'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nom')
                    ->searchable()
                    ->sortable()
                    ->label('Nom'),

                Tables\Columns\TextColumn::make('type')
                    ->sortable()
                    ->label('Type'),

                Tables\Columns\TextColumn::make('parent.nom')
                    ->sortable()
                    ->label('Zone parente'),

                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->sortable()
                    ->label('Code'),

                Tables\Columns\IconColumn::make('actif')
                    ->boolean()
                    ->sortable()
                    ->label('Actif'),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Créé le'),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Mis à jour le'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'Pays' => 'Pays',
                        'Region' => 'Région',
                        'Ville' => 'Ville',
                        'Quartier' => 'Quartier',
                    ])
                    ->label('Type'),

                Tables\Filters\SelectFilter::make('parent')
                    ->relationship('parent', 'nom')
                    ->searchable()
                    ->preload()
                    ->label('Zone parente'),

                Tables\Filters\TernaryFilter::make('actif')
                    ->label('Actif'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalDescription('Êtes-vous sûr de vouloir supprimer cette zone de livraison ? Cette action est irréversible.'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalDescription('Êtes-vous sûr de vouloir supprimer ces zones de livraison ? Cette action est irréversible.'),
                ]),
            ])
            ->defaultSort('nom');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListZoneLivraisons::route('/'),
            'create' => Pages\CreateZoneLivraison::route('/create'),
            'edit' => Pages\EditZoneLivraison::route('/{record}/edit'),
        ];
    }
}
