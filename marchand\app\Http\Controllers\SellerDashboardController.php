<?php

namespace App\Http\Controllers;

use App\Models\Marchand;
use App\Models\Commande;
use App\Models\Produit;
use App\Models\Paiement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class SellerDashboardController extends Controller
{
    /**
     * Affiche le dashboard principal du marchand
     */
    public function index()
    {
        $user = Auth::user();
        $marchand = Marchand::where('user_id', $user->id)->firstOrFail();
        
        // Statistiques générales
        $stats = $this->getStatistiques($marchand);
        
        // Commandes récentes
        $commandesRecentes = $marchand->commandes()
            ->with(['client.user', 'articles.produit'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
        
        // Produits les plus vendus
        $produitsPopulaires = $this->getProduitsPopulaires($marchand);
        
        // Graphique des ventes (30 derniers jours)
        $graphiqueVentes = $this->getGraphiqueVentes($marchand);
        
        return Inertia::render('SellerDashboard/Index', [
            'marchand' => $marchand->load(['abonnementActuel', 'user']),
            'stats' => $stats,
            'commandesRecentes' => $commandesRecentes,
            'produitsPopulaires' => $produitsPopulaires,
            'graphiqueVentes' => $graphiqueVentes,
        ]);
    }

    /**
     * Affiche la page de profil du marchand
     */
    public function profile()
    {
        $user = Auth::user();
        $marchand = Marchand::where('user_id', $user->id)
            ->with(['abonnementActuel', 'documents'])
            ->firstOrFail();
        
        return Inertia::render('SellerDashboard/Profile', [
            'marchand' => $marchand,
            'user' => $user,
        ]);
    }

    /**
     * Met à jour le profil du marchand
     */
    public function updateProfile(Request $request)
    {
        $request->validate([
            'nomEntreprise' => 'required|string|max:255',
            'description_business' => 'nullable|string|max:1000',
            'telephone_principal' => 'required|string|max:20',
            'telephone_secondaire' => 'nullable|string|max:20',
            'email_business' => 'nullable|email|max:255',
            'site_web' => 'nullable|url|max:255',
        ]);

        $user = Auth::user();
        $marchand = Marchand::where('user_id', $user->id)->firstOrFail();
        
        $marchand->update($request->only([
            'nomEntreprise',
            'description_business',
            'telephone_principal',
            'telephone_secondaire',
            'email_business',
            'site_web',
        ]));

        return back()->with('success', 'Profil mis à jour avec succès');
    }

    /**
     * Affiche la page des abonnements
     */
    public function subscriptions()
    {
        $user = Auth::user();
        $marchand = Marchand::where('user_id', $user->id)
            ->with(['abonnementActuel', 'historiqueAbonnements'])
            ->firstOrFail();
        
        // Plans d'abonnement disponibles
        $plansDisponibles = $this->getPlansAbonnement();
        
        return Inertia::render('SellerDashboard/Subscriptions', [
            'marchand' => $marchand,
            'plansDisponibles' => $plansDisponibles,
        ]);
    }

    /**
     * Affiche la page des documents
     */
    public function documents()
    {
        $user = Auth::user();
        $marchand = Marchand::where('user_id', $user->id)
            ->with(['documents.validateur'])
            ->firstOrFail();
        
        return Inertia::render('SellerDashboard/Documents', [
            'marchand' => $marchand,
            'typesDocuments' => \App\Models\MarchandDocument::getTypesDocuments(),
        ]);
    }

    /**
     * Méthodes utilitaires privées
     */

    private function getStatistiques(Marchand $marchand): array
    {
        $maintenant = Carbon::now();
        $debutMois = $maintenant->copy()->startOfMonth();
        $finMois = $maintenant->copy()->endOfMonth();
        
        // Commandes du mois
        $commandesMois = $marchand->commandes()
            ->whereBetween('created_at', [$debutMois, $finMois])
            ->count();
        
        // Revenus du mois
        $revenusMois = $marchand->paiements()
            ->where('statut', 'reussi')
            ->whereBetween('created_at', [$debutMois, $finMois])
            ->sum('montant');
        
        // Produits actifs
        $produitsActifs = $marchand->produits()
            ->where('statut', 'actif')
            ->count();
        
        // Commandes en attente
        $commandesEnAttente = $marchand->commandes()
            ->whereIn('statut', ['en_attente', 'confirmee'])
            ->count();
        
        // Évolution par rapport au mois précédent
        $debutMoisPrecedent = $debutMois->copy()->subMonth();
        $finMoisPrecedent = $debutMois->copy()->subDay();
        
        $commandesMoisPrecedent = $marchand->commandes()
            ->whereBetween('created_at', [$debutMoisPrecedent, $finMoisPrecedent])
            ->count();
        
        $revenusMoisPrecedent = $marchand->paiements()
            ->where('statut', 'reussi')
            ->whereBetween('created_at', [$debutMoisPrecedent, $finMoisPrecedent])
            ->sum('montant');
        
        return [
            'commandesMois' => $commandesMois,
            'revenusMois' => $revenusMois,
            'produitsActifs' => $produitsActifs,
            'commandesEnAttente' => $commandesEnAttente,
            'evolutionCommandes' => $commandesMoisPrecedent > 0 
                ? (($commandesMois - $commandesMoisPrecedent) / $commandesMoisPrecedent) * 100 
                : 0,
            'evolutionRevenus' => $revenusMoisPrecedent > 0 
                ? (($revenusMois - $revenusMoisPrecedent) / $revenusMoisPrecedent) * 100 
                : 0,
        ];
    }

    private function getProduitsPopulaires(Marchand $marchand): array
    {
        return DB::table('produits')
            ->join('article_commandes', 'produits.id', '=', 'article_commandes.produit_id')
            ->join('commandes', 'article_commandes.commande_id', '=', 'commandes.id')
            ->where('produits.marchand_id', $marchand->id)
            ->where('commandes.created_at', '>=', Carbon::now()->subDays(30))
            ->select(
                'produits.id',
                'produits.nom',
                'produits.prix',
                'produits.images',
                DB::raw('SUM(article_commandes.quantite) as total_vendu'),
                DB::raw('SUM(article_commandes.total) as revenus_total')
            )
            ->groupBy('produits.id', 'produits.nom', 'produits.prix', 'produits.images')
            ->orderBy('total_vendu', 'desc')
            ->limit(5)
            ->get()
            ->toArray();
    }

    private function getGraphiqueVentes(Marchand $marchand): array
    {
        $donnees = [];
        $maintenant = Carbon::now();
        
        for ($i = 29; $i >= 0; $i--) {
            $date = $maintenant->copy()->subDays($i);
            $debutJour = $date->copy()->startOfDay();
            $finJour = $date->copy()->endOfDay();
            
            $ventes = $marchand->paiements()
                ->where('statut', 'reussi')
                ->whereBetween('created_at', [$debutJour, $finJour])
                ->sum('montant');
            
            $donnees[] = [
                'date' => $date->format('Y-m-d'),
                'ventes' => (float) $ventes,
            ];
        }
        
        return $donnees;
    }

    private function getPlansAbonnement(): array
    {
        return [
            'gratuit' => [
                'nom' => 'Gratuit',
                'prix' => 0,
                'commission' => '5%',
                'limite_produits' => 50,
                'fonctionnalites' => [
                    'Jusqu\'à 50 produits',
                    'Commission de 5%',
                    'Support par email',
                    'Tableau de bord basique',
                ],
            ],
            'basique' => [
                'nom' => 'Basique',
                'prix' => 32797.85,
                'commission' => '4%',
                'limite_produits' => null,
                'fonctionnalites' => [
                    'Produits illimités',
                    'Commission de 4%',
                    'Support prioritaire',
                    'Réduction logistique 5%',
                ],
            ],
            'premium' => [
                'nom' => 'Premium',
                'prix' => 65595.70,
                'commission' => '3%',
                'limite_produits' => null,
                'fonctionnalites' => [
                    'Produits illimités',
                    'Commission de 3%',
                    'Analytics avancées',
                    'Gestionnaire dédié',
                    'Réduction logistique 10%',
                ],
            ],
            'elite' => [
                'nom' => 'Elite',
                'prix' => 131191.40,
                'commission' => '2%',
                'limite_produits' => null,
                'fonctionnalites' => [
                    'Produits illimités',
                    'Commission de 2%',
                    'IA prédictive',
                    'Événements exclusifs',
                    'Réduction logistique 15%',
                ],
            ],
        ];
    }
}
