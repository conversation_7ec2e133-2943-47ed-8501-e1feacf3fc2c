<?php

namespace App\Http\Controllers;

use App\Models\Marchand;
use App\Models\MarchandAbonnement;
use App\Models\MarchandDocument;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class SellerRegistrationController extends Controller
{
    /**
     * Affiche la page de bienvenue pour les nouveaux marchands
     */
    public function welcome()
    {
        return Inertia::render('SellerRegistration/Welcome', [
            'user' => Auth::user(),
        ]);
    }

    /**
     * Affiche le formulaire d'informations business
     */
    public function businessInfo()
    {
        $user = Auth::user();
        
        // Vérifier si l'utilisateur a déjà un marchand
        $marchand = Marchand::where('user_id', $user->id)->first();
        
        return Inertia::render('SellerRegistration/BusinessInfo', [
            'user' => $user,
            'marchand' => $marchand,
            'countries' => $this->getCountries(),
        ]);
    }

    /**
     * Sauvegarde les informations business
     */
    public function storeBusinessInfo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pays_business' => 'required|string|max:255',
            'ville_business' => 'required|string|max:255',
            'type_business' => 'required|in:individuel,entreprise,cooperative,grande_entreprise',
            'nomEntreprise' => 'required|string|max:255',
            'description_business' => 'nullable|string|max:1000',
            'telephone_principal' => 'required|string|max:20',
            'email_business' => 'nullable|email|max:255',
            'site_web' => 'nullable|url|max:255',
            'chiffre_affaires_estime' => 'nullable|numeric|min:0',
            'nombre_employes' => 'nullable|integer|min:0',
            'categories_produits' => 'nullable|array',
            'accepte_conditions' => 'required|boolean|accepted',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $user = Auth::user();
            $data = $validator->validated();
            
            // Créer ou mettre à jour le marchand
            $marchand = Marchand::updateOrCreate(
                ['user_id' => $user->id],
                [
                    'nomEntreprise' => $data['nomEntreprise'],
                    'pays_business' => $data['pays_business'],
                    'ville_business' => $data['ville_business'],
                    'type_business' => $data['type_business'],
                    'description_business' => $data['description_business'] ?? null,
                    'telephone_principal' => $data['telephone_principal'],
                    'email_business' => $data['email_business'] ?? null,
                    'site_web' => $data['site_web'] ?? null,
                    'chiffre_affaires_estime' => $data['chiffre_affaires_estime'] ?? null,
                    'nombre_employes' => $data['nombre_employes'] ?? null,
                    'categories_produits' => $data['categories_produits'] ?? [],
                    'accepte_conditions' => $data['accepte_conditions'],
                    'etape_inscription' => 'documents',
                    'statut_validation' => 'en_attente',
                    'documents_requis' => $this->getDocumentsRequis($data['type_business']),
                    'source_inscription' => 'seller_platform',
                    'accepte_newsletter' => true,
                    'langue_preferee' => 'fr',
                ]
            );

            DB::commit();

            return redirect()->route('seller.documents')->with('success', 'Informations business sauvegardées avec succès');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Une erreur est survenue lors de la sauvegarde'])->withInput();
        }
    }

    /**
     * Affiche la page d'upload des documents
     */
    public function documents()
    {
        $user = Auth::user();
        $marchand = Marchand::where('user_id', $user->id)->firstOrFail();
        
        $documentsRequis = $marchand->getDocumentsRequis();
        $documentsExistants = $marchand->documents()->get()->keyBy('type_document');
        
        return Inertia::render('SellerRegistration/Documents', [
            'marchand' => $marchand,
            'documentsRequis' => $documentsRequis,
            'documentsExistants' => $documentsExistants,
            'typesDocuments' => MarchandDocument::getTypesDocuments(),
            'extensionsAutorisees' => MarchandDocument::getExtensionsAutorisees(),
            'tailleMaximale' => MarchandDocument::getTailleMaximale(),
        ]);
    }

    /**
     * Upload un document
     */
    public function uploadDocument(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type_document' => 'required|string',
            'fichier' => 'required|file|max:' . (MarchandDocument::getTailleMaximale() / 1024) . '|mimes:' . implode(',', MarchandDocument::getExtensionsAutorisees()),
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            DB::beginTransaction();

            $user = Auth::user();
            $marchand = Marchand::where('user_id', $user->id)->firstOrFail();
            
            $file = $request->file('fichier');
            $typeDocument = $request->type_document;
            
            // Générer un nom unique pour le fichier
            $nomStockage = uniqid() . '_' . time() . '.' . $file->getClientOriginalExtension();
            $cheminFichier = "documents/marchands/{$marchand->id}/{$nomStockage}";
            
            // Stocker le fichier
            $path = Storage::putFileAs("documents/marchands/{$marchand->id}", $file, $nomStockage);
            
            // Calculer le hash du fichier
            $contenuFichier = $file->getContent();
            $hashFichier = hash('sha256', $contenuFichier);
            
            // Supprimer l'ancien document du même type s'il existe
            $ancienDocument = $marchand->documents()->where('type_document', $typeDocument)->first();
            if ($ancienDocument) {
                Storage::delete($ancienDocument->chemin_fichier);
                $ancienDocument->delete();
            }
            
            // Créer l'enregistrement du document
            $document = MarchandDocument::create([
                'marchand_id' => $marchand->id,
                'type_document' => $typeDocument,
                'nom_original' => $file->getClientOriginalName(),
                'nom_stockage' => $nomStockage,
                'chemin_fichier' => $cheminFichier,
                'extension' => $file->getClientOriginalExtension(),
                'taille_fichier' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'hash_fichier' => $hashFichier,
                'statut_validation' => 'en_attente',
                'date_upload' => now(),
                'est_obligatoire' => in_array($typeDocument, $marchand->getDocumentsRequis()),
                'est_confidentiel' => true,
                'version' => 1,
                'adresse_ip_upload' => $request->ip(),
                'user_agent_upload' => $request->userAgent(),
            ]);

            // Mettre à jour le statut du marchand si tous les documents sont uploadés
            $this->verifierCompletionDocuments($marchand);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Document uploadé avec succès',
                'document' => $document,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Erreur lors de l\'upload du document'], 500);
        }
    }

    /**
     * Finalise l'inscription
     */
    public function finalize()
    {
        $user = Auth::user();
        $marchand = Marchand::where('user_id', $user->id)->firstOrFail();
        
        // Vérifier que tous les documents obligatoires sont uploadés
        if (!$marchand->documentsObligatoiresComplets()) {
            return back()->withErrors(['error' => 'Tous les documents obligatoires doivent être uploadés']);
        }

        try {
            DB::beginTransaction();

            // Mettre à jour le statut
            $marchand->update([
                'etape_inscription' => 'validation',
                'date_soumission_documents' => now(),
                'documents_soumis' => $marchand->documents()->pluck('type_document')->toArray(),
            ]);

            // Créer un abonnement gratuit par défaut
            MarchandAbonnement::creerAbonnement($marchand->id, 'gratuit');

            DB::commit();

            return Inertia::render('SellerRegistration/Success', [
                'marchand' => $marchand,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Erreur lors de la finalisation']);
        }
    }

    /**
     * Méthodes utilitaires privées
     */

    private function getCountries(): array
    {
        return [
            'CM' => 'Cameroun',
            'CI' => 'Côte d\'Ivoire',
            'SN' => 'Sénégal',
            'ML' => 'Mali',
            'BF' => 'Burkina Faso',
            'NE' => 'Niger',
            'TD' => 'Tchad',
            'GA' => 'Gabon',
            'CG' => 'Congo',
            'CF' => 'République Centrafricaine',
        ];
    }

    private function getDocumentsRequis(string $typeBusiness): array
    {
        $documentsBase = ['CNI', 'photo_avec_cni'];

        switch ($typeBusiness) {
            case 'individuel':
                return array_merge($documentsBase, ['telephone_verification']);

            case 'entreprise':
                return array_merge($documentsBase, [
                    'registre_commerce',
                    'statuts_entreprise',
                    'rib_bancaire'
                ]);

            case 'cooperative':
                return array_merge($documentsBase, [
                    'recepisse_cooperative',
                    'statuts_cooperative',
                    'rib_bancaire'
                ]);

            case 'grande_entreprise':
                return array_merge($documentsBase, [
                    'bilan_comptable',
                    'registre_commerce',
                    'rib_bancaire',
                    'attestation_fiscale'
                ]);

            default:
                return $documentsBase;
        }
    }

    private function verifierCompletionDocuments(Marchand $marchand): void
    {
        $documentsRequis = $marchand->getDocumentsRequis();
        $documentsUploades = $marchand->documents()->pluck('type_document')->toArray();
        
        $tousDocumentsUploades = empty(array_diff($documentsRequis, $documentsUploades));
        
        if ($tousDocumentsUploades && $marchand->etape_inscription === 'documents') {
            $marchand->update(['etape_inscription' => 'pret_validation']);
        }
    }
}
