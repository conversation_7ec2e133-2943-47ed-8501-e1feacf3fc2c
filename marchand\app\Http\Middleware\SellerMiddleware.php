<?php

namespace App\Http\Middleware;

use App\Models\Marchand;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SellerMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Vérifier que l'utilisateur est connecté
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        
        // Vérifier que l'utilisateur a un profil marchand
        $marchand = Marchand::where('user_id', $user->id)->first();
        
        if (!$marchand) {
            // Rediriger vers le processus d'inscription marchand
            return redirect()->route('seller.welcome');
        }

        // Vérifier le statut de validation du marchand
        switch ($marchand->statut_validation) {
            case 'en_attente':
                // Si l'inscription n'est pas complète, rediriger vers l'étape appropriée
                if ($marchand->etape_inscription === 'documents') {
                    return redirect()->route('seller.documents');
                } elseif ($marchand->etape_inscription === 'business_info') {
                    return redirect()->route('seller.business-info');
                } else {
                    // En attente de validation
                    return redirect()->route('seller.finalize');
                }
                break;

            case 'rejete':
                // Marchand rejeté, rediriger vers une page d'information
                return redirect()->route('seller.rejected');
                break;

            case 'suspendu':
                // Marchand suspendu, rediriger vers une page d'information
                return redirect()->route('seller.suspended');
                break;

            case 'valide':
                // Marchand validé, continuer
                break;

            default:
                // Statut inconnu, rediriger vers l'inscription
                return redirect()->route('seller.welcome');
        }

        // Vérifier que le marchand a un abonnement actif
        if (!$marchand->aAbonnementActif()) {
            // Rediriger vers la page d'abonnement
            return redirect()->route('dashboard.subscriptions')
                ->with('warning', 'Vous devez avoir un abonnement actif pour accéder à cette section.');
        }

        return $next($request);
    }
}
