<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Adresse extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'rue',
        'ville',
        'etat',
        'pays',
        'codePostal',
        'type',
        'zone_livraison_id',
    ];

    /**
     * Relations
     */

    /**
     * Utilisateur propriétaire de l'adresse
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Zone de livraison associée
     */
    public function zoneLivraison(): BelongsTo
    {
        return $this->belongsTo(ZoneLivraison::class, 'zone_livraison_id');
    }

    /**
     * Marchands utilisant cette adresse
     */
    public function marchands(): HasMany
    {
        return $this->hasMany(Marchand::class);
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Obtient l'adresse formatée
     */
    public function getAdresseComplete(): string
    {
        return trim("{$this->rue}, {$this->ville}, {$this->etat}, {$this->pays} {$this->codePostal}");
    }

    /**
     * Vérifie si l'adresse est dans une zone de livraison
     */
    public function estDansZoneLivraison(): bool
    {
        return $this->zone_livraison_id !== null;
    }
}
