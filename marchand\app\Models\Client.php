<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Client extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'telephone',
        'dateNaissance',
        'genre',
        'preferences',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'dateNaissance' => 'date',
        'preferences' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Utilisateur associé au client
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Commandes du client
     */
    public function commandes(): HasMany
    {
        return $this->hasMany(Commande::class);
    }
}
