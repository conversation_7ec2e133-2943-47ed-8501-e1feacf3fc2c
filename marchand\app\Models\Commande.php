<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Commande extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'client_id',
        'marchand_id',
        'adresse_id',
        'total',
        'statut',
        'date_commande',
        'date_livraison_prevue',
        'date_livraison_reelle',
        'frais_livraison',
        'zone_livraison_id',
        'transporteur',
        'numero_suivi',
        'instructions_livraison',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'total' => 'decimal:2',
        'frais_livraison' => 'decimal:2',
        'date_commande' => 'datetime',
        'date_livraison_prevue' => 'datetime',
        'date_livraison_reelle' => 'datetime',
    ];

    /**
     * Relations
     */

    /**
     * Client qui a passé la commande
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Marchand qui traite la commande
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Adresse de livraison
     */
    public function adresse(): BelongsTo
    {
        return $this->belongsTo(Adresse::class);
    }

    /**
     * Zone de livraison
     */
    public function zoneLivraison(): BelongsTo
    {
        return $this->belongsTo(ZoneLivraison::class, 'zone_livraison_id');
    }

    /**
     * Articles de la commande
     */
    public function articles(): HasMany
    {
        return $this->hasMany(ArticleCommande::class);
    }

    /**
     * Paiements de la commande
     */
    public function paiements(): HasMany
    {
        return $this->hasMany(Paiement::class);
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Obtient le total formaté
     */
    public function getTotalFormate(): string
    {
        return number_format($this->total, 2) . ' FCFA';
    }

    /**
     * Vérifie si la commande est payée
     */
    public function estPayee(): bool
    {
        return $this->statut === 'payee' || $this->statut === 'expediee' || $this->statut === 'livree';
    }

    /**
     * Vérifie si la commande est livrée
     */
    public function estLivree(): bool
    {
        return $this->statut === 'livree' && $this->date_livraison_reelle !== null;
    }

    /**
     * Calcule le nombre de jours depuis la commande
     */
    public function getJoursDepuisCommande(): int
    {
        return $this->date_commande->diffInDays(now());
    }
}
