<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Marchand extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'nomEntreprise',
        'adresse_id',
        'idFiscal',
        'banqueNom',
        'banqueNumeroCompte',
        // Nouvelles colonnes pour la plateforme seller
        'pays_business',
        'ville_business',
        'type_business',
        'statut_validation',
        'etape_inscription',
        'documents_soumis',
        'documents_requis',
        'date_soumission_documents',
        'date_validation',
        'commentaires_validation',
        'validateur_id',
        'telephone_principal',
        'telephone_secondaire',
        'email_business',
        'site_web',
        'methode_paiement_preferee',
        'iban_crypte',
        'nom_titulaire_compte',
        'numero_orange_money',
        'numero_mtn_money',
        'description_business',
        'categories_produits',
        'chiffre_affaires_estime',
        'nombre_employes',
        'accepte_conditions',
        'accepte_newsletter',
        'langue_preferee',
        'notifications_preferences',
        'source_inscription',
        'code_parrainage',
        'parrain_id',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function adresse(): BelongsTo
    {
        return $this->belongsTo(Adresse::class);
    }

    public function produits(): HasMany
    {
        return $this->hasMany(Produit::class);
    }

    public function commandes(): HasMany
    {
        return $this->hasMany(Commande::class);
    }

    public function paiements(): HasMany
    {
        return $this->hasMany(Paiement::class);
    }

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'documents_soumis' => 'array',
        'documents_requis' => 'array',
        'categories_produits' => 'array',
        'notifications_preferences' => 'array',
        'date_soumission_documents' => 'datetime',
        'date_validation' => 'datetime',
        'chiffre_affaires_estime' => 'decimal:2',
        'accepte_conditions' => 'boolean',
        'accepte_newsletter' => 'boolean',
    ];

    /**
     * Relations pour la plateforme seller
     */

    /**
     * Validateur qui a validé le marchand
     */
    public function validateur(): BelongsTo
    {
        return $this->belongsTo(User::class, 'validateur_id');
    }

    /**
     * Marchand parrain
     */
    public function parrain(): BelongsTo
    {
        return $this->belongsTo(Marchand::class, 'parrain_id');
    }

    /**
     * Marchands parrainés
     */
    public function filleuls(): HasMany
    {
        return $this->hasMany(Marchand::class, 'parrain_id');
    }

    /**
     * Abonnement actuel du marchand
     */
    public function abonnementActuel()
    {
        return $this->hasOne(MarchandAbonnement::class)->where('statut', 'actif')->latest();
    }

    /**
     * Tous les abonnements du marchand
     */
    public function abonnements(): HasMany
    {
        return $this->hasMany(MarchandAbonnement::class);
    }

    /**
     * Historique des abonnements
     */
    public function historiqueAbonnements(): HasMany
    {
        return $this->hasMany(MarchandAbonnementHistorique::class);
    }

    /**
     * Documents du marchand
     */
    public function documents(): HasMany
    {
        return $this->hasMany(MarchandDocument::class);
    }

    /**
     * Documents validés
     */
    public function documentsValides(): HasMany
    {
        return $this->hasMany(MarchandDocument::class)->where('statut_validation', 'valide');
    }

    /**
     * Documents en attente
     */
    public function documentsEnAttente(): HasMany
    {
        return $this->hasMany(MarchandDocument::class)->where('statut_validation', 'en_attente');
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Vérifie si le marchand est validé
     */
    public function estValide(): bool
    {
        return $this->statut_validation === 'valide';
    }

    /**
     * Vérifie si le marchand a un abonnement actif
     */
    public function aAbonnementActif(): bool
    {
        return $this->abonnementActuel()->exists();
    }

    /**
     * Vérifie si tous les documents obligatoires sont complets
     */
    public function documentsObligatoiresComplets(): bool
    {
        $documentsRequis = $this->getDocumentsRequis();
        $documentsValides = $this->documentsValides()->pluck('type_document')->toArray();

        foreach ($documentsRequis as $typeDocument) {
            if (!in_array($typeDocument, $documentsValides)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Obtient la liste des documents requis selon le type de business
     */
    public function getDocumentsRequis(): array
    {
        $documentsBase = ['CNI', 'photo_avec_cni'];

        switch ($this->type_business) {
            case 'individuel':
                return array_merge($documentsBase, ['telephone_verification']);

            case 'entreprise':
                return array_merge($documentsBase, [
                    'registre_commerce',
                    'statuts_entreprise',
                    'rib_bancaire'
                ]);

            case 'cooperative':
                return array_merge($documentsBase, [
                    'recepisse_cooperative',
                    'statuts_cooperative',
                    'rib_bancaire'
                ]);

            case 'grande_entreprise':
                return array_merge($documentsBase, [
                    'bilan_comptable',
                    'registre_commerce',
                    'rib_bancaire',
                    'attestation_fiscale'
                ]);

            default:
                return $documentsBase;
        }
    }

    /**
     * Calcule le taux de commission selon l'abonnement
     */
    public function getTauxCommission(): float
    {
        $abonnement = $this->abonnementActuel;
        if (!$abonnement) {
            return 10.0; // Taux par défaut pour les marchands sans abonnement
        }

        return $abonnement->commission_taux_min;
    }

    /**
     * Vérifie si le marchand peut accéder à une fonctionnalité
     */
    public function peutAcceder(string $fonctionnalite): bool
    {
        $abonnement = $this->abonnementActuel;
        if (!$abonnement) {
            return false;
        }

        return $abonnement->{'acces_' . $fonctionnalite} ?? false;
    }
}
