<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MarchandAbonnementHistorique extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'marchand_abonnement_historique';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'marchand_id',
        'abonnement_id',
        'action',
        'type_abonnement_avant',
        'type_abonnement_apres',
        'statut_avant',
        'statut_apres',
        'prix_avant',
        'prix_apres',
        'date_action',
        'initie_par',
        'raison',
        'montant_paye',
        'montant_rembourse',
        'reference_paiement',
        'methode_paiement',
        'details_changement',
        'adresse_ip',
        'user_agent',
        'metadonnees',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'date_action' => 'datetime',
        'prix_avant' => 'decimal:2',
        'prix_apres' => 'decimal:2',
        'montant_paye' => 'decimal:2',
        'montant_rembourse' => 'decimal:2',
        'details_changement' => 'array',
        'metadonnees' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Marchand concerné par l'historique
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Abonnement concerné
     */
    public function abonnement(): BelongsTo
    {
        return $this->belongsTo(MarchandAbonnement::class, 'abonnement_id');
    }

    /**
     * Utilisateur qui a initié l'action
     */
    public function initiateur(): BelongsTo
    {
        return $this->belongsTo(User::class, 'initie_par');
    }

    /**
     * Méthodes statiques pour enregistrer les actions
     */

    /**
     * Enregistre un changement d'abonnement
     */
    public static function enregistrerChangementAbonnement(
        int $marchandId,
        int $abonnementId,
        string $action,
        ?string $typeAvant = null,
        ?string $typeApres = null,
        ?string $raison = null,
        ?int $initieParId = null
    ): self {
        return self::create([
            'marchand_id' => $marchandId,
            'abonnement_id' => $abonnementId,
            'action' => $action,
            'type_abonnement_avant' => $typeAvant,
            'type_abonnement_apres' => $typeApres,
            'date_action' => now(),
            'initie_par' => $initieParId ?? auth()->id(),
            'raison' => $raison,
            'adresse_ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Enregistre un paiement
     */
    public static function enregistrerPaiement(
        int $marchandId,
        int $abonnementId,
        float $montant,
        string $referencePaiement,
        string $methodePaiement,
        string $statut = 'paye'
    ): self {
        return self::create([
            'marchand_id' => $marchandId,
            'abonnement_id' => $abonnementId,
            'action' => 'paiement',
            'date_action' => now(),
            'montant_paye' => $montant,
            'reference_paiement' => $referencePaiement,
            'methode_paiement' => $methodePaiement,
            'statut_apres' => $statut,
            'initie_par' => auth()->id(),
            'adresse_ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Enregistre un remboursement
     */
    public static function enregistrerRemboursement(
        int $marchandId,
        int $abonnementId,
        float $montant,
        string $raison,
        ?string $referencePaiement = null
    ): self {
        return self::create([
            'marchand_id' => $marchandId,
            'abonnement_id' => $abonnementId,
            'action' => 'remboursement',
            'date_action' => now(),
            'montant_rembourse' => $montant,
            'reference_paiement' => $referencePaiement,
            'raison' => $raison,
            'initie_par' => auth()->id(),
            'adresse_ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Méthodes d'analyse
     */

    /**
     * Obtient un résumé de l'historique pour un marchand
     */
    public static function getResumeMarchand(int $marchandId): array
    {
        $historique = self::where('marchand_id', $marchandId)
            ->orderBy('date_action', 'desc')
            ->get();

        $totalPaiements = $historique->where('action', 'paiement')->sum('montant_paye');
        $totalRemboursements = $historique->where('action', 'remboursement')->sum('montant_rembourse');
        $nombreChangements = $historique->whereIn('action', ['upgrade', 'downgrade', 'changement'])->count();

        return [
            'total_paiements' => $totalPaiements,
            'total_remboursements' => $totalRemboursements,
            'solde_net' => $totalPaiements - $totalRemboursements,
            'nombre_changements' => $nombreChangements,
            'derniere_action' => $historique->first(),
            'historique_complet' => $historique,
        ];
    }

    /**
     * Actions possibles
     */
    public static function getActionsDisponibles(): array
    {
        return [
            'creation' => 'Création d\'abonnement',
            'upgrade' => 'Mise à niveau',
            'downgrade' => 'Rétrogradation',
            'renouvellement' => 'Renouvellement',
            'suspension' => 'Suspension',
            'reactivation' => 'Réactivation',
            'annulation' => 'Annulation',
            'paiement' => 'Paiement',
            'remboursement' => 'Remboursement',
            'changement' => 'Changement général',
        ];
    }

    /**
     * Obtient le libellé de l'action
     */
    public function getLibelleAction(): string
    {
        $actions = self::getActionsDisponibles();
        return $actions[$this->action] ?? $this->action;
    }

    /**
     * Vérifie si l'action est un paiement
     */
    public function estPaiement(): bool
    {
        return $this->action === 'paiement' && $this->montant_paye > 0;
    }

    /**
     * Vérifie si l'action est un remboursement
     */
    public function estRemboursement(): bool
    {
        return $this->action === 'remboursement' && $this->montant_rembourse > 0;
    }

    /**
     * Obtient le montant net de l'action (positif pour paiement, négatif pour remboursement)
     */
    public function getMontantNet(): float
    {
        if ($this->estPaiement()) {
            return $this->montant_paye;
        }
        
        if ($this->estRemboursement()) {
            return -$this->montant_rembourse;
        }

        return 0.0;
    }
}
