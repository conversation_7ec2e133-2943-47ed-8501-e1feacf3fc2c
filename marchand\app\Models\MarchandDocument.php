<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Crypt;

class MarchandDocument extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'marchand_documents';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'marchand_id',
        'type_document',
        'nom_original',
        'nom_stockage',
        'chemin_fichier',
        'extension',
        'taille_fichier',
        'mime_type',
        'hash_fichier',
        'statut_validation',
        'validateur_id',
        'date_validation',
        'commentaires_validation',
        'raison_rejet',
        'date_upload',
        'date_expiration',
        'date_derniere_modification',
        'est_obligatoire',
        'est_confidentiel',
        'version',
        'metadonnees',
        'est_crypte',
        'cle_cryptage',
        'adresse_ip_upload',
        'user_agent_upload',
        'ocr_effectue',
        'donnees_ocr',
        'score_qualite',
        'verifications_automatiques',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'date_upload' => 'datetime',
        'date_validation' => 'datetime',
        'date_expiration' => 'datetime',
        'date_derniere_modification' => 'datetime',
        'est_obligatoire' => 'boolean',
        'est_confidentiel' => 'boolean',
        'est_crypte' => 'boolean',
        'ocr_effectue' => 'boolean',
        'score_qualite' => 'decimal:2',
        'metadonnees' => 'array',
        'donnees_ocr' => 'array',
        'verifications_automatiques' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Marchand propriétaire du document
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Validateur du document
     */
    public function validateur(): BelongsTo
    {
        return $this->belongsTo(User::class, 'validateur_id');
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Valide le document
     */
    public function valider(int $validateurId, string $commentaires = null): bool
    {
        $this->update([
            'statut_validation' => 'valide',
            'validateur_id' => $validateurId,
            'date_validation' => now(),
            'commentaires_validation' => $commentaires,
            'raison_rejet' => null,
        ]);

        return true;
    }

    /**
     * Rejette le document
     */
    public function rejeter(int $validateurId, string $raison, string $commentaires = null): bool
    {
        $this->update([
            'statut_validation' => 'rejete',
            'validateur_id' => $validateurId,
            'date_validation' => now(),
            'raison_rejet' => $raison,
            'commentaires_validation' => $commentaires,
        ]);

        return true;
    }

    /**
     * Obtient l'URL sécurisée du document
     */
    public function getUrlSecurisee(): string
    {
        if (!Storage::exists($this->chemin_fichier)) {
            throw new \Exception('Fichier non trouvé');
        }

        // Générer une URL temporaire sécurisée
        return Storage::temporaryUrl($this->chemin_fichier, now()->addMinutes(30));
    }

    /**
     * Vérifie si le document est expiré
     */
    public function estExpire(): bool
    {
        return $this->date_expiration && $this->date_expiration->isPast();
    }

    /**
     * Obtient la taille formatée du fichier
     */
    public function getTailleFormatee(): string
    {
        $bytes = $this->taille_fichier;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Vérifie l'intégrité du fichier
     */
    public function verifierIntegrite(): bool
    {
        if (!Storage::exists($this->chemin_fichier)) {
            return false;
        }

        $contenuFichier = Storage::get($this->chemin_fichier);
        $hashActuel = hash('sha256', $contenuFichier);

        return $hashActuel === $this->hash_fichier;
    }

    /**
     * Crypte le contenu du document
     */
    public function crypter(): bool
    {
        if ($this->est_crypte) {
            return true; // Déjà crypté
        }

        if (!Storage::exists($this->chemin_fichier)) {
            return false;
        }

        $contenu = Storage::get($this->chemin_fichier);
        $contenuCrypte = Crypt::encrypt($contenu);
        
        // Sauvegarder le contenu crypté
        Storage::put($this->chemin_fichier, $contenuCrypte);
        
        $this->update([
            'est_crypte' => true,
            'cle_cryptage' => 'laravel_default', // Utilise la clé par défaut de Laravel
        ]);

        return true;
    }

    /**
     * Décrypte le contenu du document
     */
    public function decrypter(): string
    {
        if (!$this->est_crypte) {
            return Storage::get($this->chemin_fichier);
        }

        $contenuCrypte = Storage::get($this->chemin_fichier);
        return Crypt::decrypt($contenuCrypte);
    }

    /**
     * Types de documents autorisés
     */
    public static function getTypesDocuments(): array
    {
        return [
            'CNI' => 'Carte d\'identité nationale',
            'passeport' => 'Passeport',
            'photo_avec_cni' => 'Photo tenant la CNI',
            'registre_commerce' => 'Registre de commerce',
            'statuts_entreprise' => 'Statuts de l\'entreprise',
            'rib_bancaire' => 'RIB bancaire',
            'recepisse_cooperative' => 'Récépissé de coopérative',
            'statuts_cooperative' => 'Statuts de coopérative',
            'bilan_comptable' => 'Bilan comptable',
            'attestation_fiscale' => 'Attestation fiscale',
            'telephone_verification' => 'Vérification téléphone',
            'autre' => 'Autre document',
        ];
    }

    /**
     * Extensions de fichiers autorisées
     */
    public static function getExtensionsAutorisees(): array
    {
        return ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'];
    }

    /**
     * Taille maximale de fichier (en bytes)
     */
    public static function getTailleMaximale(): int
    {
        return 10 * 1024 * 1024; // 10 MB
    }
}
