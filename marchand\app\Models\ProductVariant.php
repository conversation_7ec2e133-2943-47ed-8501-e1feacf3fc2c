<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductVariant extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'product_variants';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'produit_id',
        'nom',
        'prix',
        'quantite',
        'sku',
        'attributs',
        'actif',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'prix' => 'decimal:2',
        'quantite' => 'integer',
        'attributs' => 'array',
        'actif' => 'boolean',
    ];

    /**
     * Relations
     */

    /**
     * Produit parent
     */
    public function produit(): BelongsTo
    {
        return $this->belongsTo(Produit::class);
    }
}
