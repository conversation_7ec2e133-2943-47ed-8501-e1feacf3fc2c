<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Produit extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'marchand_id',
        'categorie_id',
        'nom',
        'description',
        'prix',
        'quantite',
        'images',
        'slug',
        'statut',
        'featured',
        'currency',
        'product_code',
        'brand',
        'attributs',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'images' => 'array',
        'prix' => 'decimal:2',
        'quantite' => 'integer',
        'featured' => 'boolean',
        'attributs' => 'array',
    ];

    /**
     * Relations
     */

    /**
     * Marchand propriétaire du produit
     */
    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    /**
     * Catégorie du produit
     */
    public function categorie(): BelongsTo
    {
        return $this->belongsTo(Categorie::class);
    }

    /**
     * Variantes du produit
     */
    public function variantes(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Avis sur le produit
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Méthodes utilitaires
     */

    /**
     * Vérifie si le produit est en stock
     */
    public function estEnStock(): bool
    {
        return $this->quantite > 0;
    }

    /**
     * Obtient le prix formaté
     */
    public function getPrixFormate(): string
    {
        return number_format($this->prix, 2) . ' ' . ($this->currency ?? 'FCFA');
    }

    /**
     * Vérifie si le produit est mis en avant
     */
    public function estFeatured(): bool
    {
        return $this->featured === true;
    }

    /**
     * Obtient l'URL de la première image
     */
    public function getPremierImage(): ?string
    {
        if (empty($this->images)) {
            return null;
        }

        return $this->images[0] ?? null;
    }
}
