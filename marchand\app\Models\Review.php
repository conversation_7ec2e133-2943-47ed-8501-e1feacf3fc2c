<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Review extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'produit_id',
        'user_id',
        'note',
        'commentaire',
        'statut',
        'date_review',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'note' => 'integer',
        'date_review' => 'datetime',
    ];

    /**
     * Relations
     */

    /**
     * Produit évalué
     */
    public function produit(): BelongsTo
    {
        return $this->belongsTo(Produit::class);
    }

    /**
     * Utilisateur qui a fait l'évaluation
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
