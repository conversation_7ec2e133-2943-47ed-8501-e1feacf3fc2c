<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ZoneLivraison extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'zones_livraison';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'nom',
        'type',
        'parent_id',
        'code',
        'actif',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'actif' => 'boolean',
    ];

    /**
     * Relations
     */

    /**
     * Zone parent
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(ZoneLivraison::class, 'parent_id');
    }

    /**
     * Zones enfants
     */
    public function enfants(): HasMany
    {
        return $this->hasMany(ZoneLivraison::class, 'parent_id');
    }

    /**
     * Adresses dans cette zone
     */
    public function adresses(): HasMany
    {
        return $this->hasMany(Adresse::class, 'zone_livraison_id');
    }

    /**
     * Commandes dans cette zone
     */
    public function commandes(): HasMany
    {
        return $this->hasMany(Commande::class, 'zone_livraison_id');
    }
}
