<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('marchands', function (Blueprint $table) {
            // Nouvelles colonnes pour la plateforme seller
            $table->string('pays_business')->nullable()->after('banqueNumeroCompte');
            $table->string('ville_business')->nullable()->after('pays_business');
            $table->enum('type_business', ['individuel', 'entreprise', 'cooperative', 'grande_entreprise'])->nullable()->after('ville_business');
            $table->enum('statut_validation', ['en_attente', 'valide', 'rejete', 'suspendu'])->default('en_attente')->after('type_business');
            $table->enum('etape_inscription', ['business_info', 'documents', 'pret_validation', 'validation', 'complete'])->default('business_info')->after('statut_validation');
            
            // Documents et validation
            $table->json('documents_soumis')->nullable()->after('etape_inscription');
            $table->json('documents_requis')->nullable()->after('documents_soumis');
            $table->timestamp('date_soumission_documents')->nullable()->after('documents_requis');
            $table->timestamp('date_validation')->nullable()->after('date_soumission_documents');
            $table->text('commentaires_validation')->nullable()->after('date_validation');
            $table->unsignedBigInteger('validateur_id')->nullable()->after('commentaires_validation');
            
            // Informations de contact étendues
            $table->string('telephone_principal')->nullable()->after('validateur_id');
            $table->string('telephone_secondaire')->nullable()->after('telephone_principal');
            $table->string('email_business')->nullable()->after('telephone_secondaire');
            $table->string('site_web')->nullable()->after('email_business');
            
            // Informations de paiement
            $table->enum('methode_paiement_preferee', ['orange_money', 'mtn_money', 'virement_bancaire', 'autre'])->nullable()->after('site_web');
            $table->text('iban_crypte')->nullable()->after('methode_paiement_preferee');
            $table->string('nom_titulaire_compte')->nullable()->after('iban_crypte');
            $table->string('numero_orange_money')->nullable()->after('nom_titulaire_compte');
            $table->string('numero_mtn_money')->nullable()->after('numero_orange_money');
            
            // Informations business étendues
            $table->text('description_business')->nullable()->after('numero_mtn_money');
            $table->json('categories_produits')->nullable()->after('description_business');
            $table->decimal('chiffre_affaires_estime', 15, 2)->nullable()->after('categories_produits');
            $table->integer('nombre_employes')->nullable()->after('chiffre_affaires_estime');
            
            // Préférences et consentements
            $table->boolean('accepte_conditions')->default(false)->after('nombre_employes');
            $table->boolean('accepte_newsletter')->default(false)->after('accepte_conditions');
            $table->string('langue_preferee', 5)->default('fr')->after('accepte_newsletter');
            $table->json('notifications_preferences')->nullable()->after('langue_preferee');
            
            // Tracking et parrainage
            $table->string('source_inscription')->nullable()->after('notifications_preferences');
            $table->string('code_parrainage')->nullable()->after('source_inscription');
            $table->unsignedBigInteger('parrain_id')->nullable()->after('code_parrainage');
            
            // Index pour les performances
            $table->index(['statut_validation']);
            $table->index(['etape_inscription']);
            $table->index(['type_business']);
            $table->index(['pays_business']);
            $table->index(['validateur_id']);
            $table->index(['parrain_id']);
            
            // Clés étrangères
            $table->foreign('validateur_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('parrain_id')->references('id')->on('marchands')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('marchands', function (Blueprint $table) {
            // Supprimer les clés étrangères
            $table->dropForeign(['validateur_id']);
            $table->dropForeign(['parrain_id']);
            
            // Supprimer les index
            $table->dropIndex(['statut_validation']);
            $table->dropIndex(['etape_inscription']);
            $table->dropIndex(['type_business']);
            $table->dropIndex(['pays_business']);
            $table->dropIndex(['validateur_id']);
            $table->dropIndex(['parrain_id']);
            
            // Supprimer les colonnes
            $table->dropColumn([
                'pays_business',
                'ville_business',
                'type_business',
                'statut_validation',
                'etape_inscription',
                'documents_soumis',
                'documents_requis',
                'date_soumission_documents',
                'date_validation',
                'commentaires_validation',
                'validateur_id',
                'telephone_principal',
                'telephone_secondaire',
                'email_business',
                'site_web',
                'methode_paiement_preferee',
                'iban_crypte',
                'nom_titulaire_compte',
                'numero_orange_money',
                'numero_mtn_money',
                'description_business',
                'categories_produits',
                'chiffre_affaires_estime',
                'nombre_employes',
                'accepte_conditions',
                'accepte_newsletter',
                'langue_preferee',
                'notifications_preferences',
                'source_inscription',
                'code_parrainage',
                'parrain_id',
            ]);
        });
    }
};
