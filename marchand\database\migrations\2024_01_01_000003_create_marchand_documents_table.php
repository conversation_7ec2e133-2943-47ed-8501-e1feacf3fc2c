<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marchand_documents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('marchand_id');
            
            // Informations du document
            $table->string('type_document'); // CNI, passeport, registre_commerce, etc.
            $table->string('nom_original');
            $table->string('nom_stockage');
            $table->string('chemin_fichier');
            $table->string('extension', 10);
            $table->bigInteger('taille_fichier'); // en bytes
            $table->string('mime_type');
            $table->string('hash_fichier'); // Pour vérifier l'intégrité
            
            // Validation et statut
            $table->enum('statut_validation', ['en_attente', 'valide', 'rejete'])->default('en_attente');
            $table->unsignedBigInteger('validateur_id')->nullable();
            $table->timestamp('date_validation')->nullable();
            $table->text('commentaires_validation')->nullable();
            $table->text('raison_rejet')->nullable();
            
            // Métadonnées temporelles
            $table->timestamp('date_upload');
            $table->timestamp('date_expiration')->nullable();
            $table->timestamp('date_derniere_modification')->nullable();
            
            // Propriétés du document
            $table->boolean('est_obligatoire')->default(false);
            $table->boolean('est_confidentiel')->default(true);
            $table->integer('version')->default(1);
            $table->json('metadonnees')->nullable(); // Métadonnées personnalisées
            
            // Sécurité et cryptage
            $table->boolean('est_crypte')->default(false);
            $table->string('cle_cryptage')->nullable();
            
            // Tracking
            $table->ipAddress('adresse_ip_upload')->nullable();
            $table->text('user_agent_upload')->nullable();
            
            // OCR et analyse automatique
            $table->boolean('ocr_effectue')->default(false);
            $table->json('donnees_ocr')->nullable();
            $table->decimal('score_qualite', 3, 2)->nullable(); // Score de 0 à 1
            $table->json('verifications_automatiques')->nullable();
            
            $table->timestamps();
            
            // Index pour les performances
            $table->index(['marchand_id', 'type_document']);
            $table->index(['statut_validation']);
            $table->index(['validateur_id']);
            $table->index(['date_upload']);
            $table->index(['est_obligatoire']);
            $table->index(['hash_fichier']);
            
            // Contrainte d'unicité : un seul document par type par marchand
            $table->unique(['marchand_id', 'type_document']);
            
            // Clés étrangères
            $table->foreign('marchand_id')->references('id')->on('marchands')->onDelete('cascade');
            $table->foreign('validateur_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marchand_documents');
    }
};
