<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marchand_abonnement_historique', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('marchand_id');
            $table->unsignedBigInteger('abonnement_id')->nullable();
            
            // Type d'action effectuée
            $table->enum('action', [
                'creation',
                'upgrade',
                'downgrade',
                'renouvellement',
                'suspension',
                'reactivation',
                'annulation',
                'paiement',
                'remboursement',
                'changement'
            ]);
            
            // États avant/après
            $table->string('type_abonnement_avant')->nullable();
            $table->string('type_abonnement_apres')->nullable();
            $table->string('statut_avant')->nullable();
            $table->string('statut_apres')->nullable();
            $table->decimal('prix_avant', 10, 2)->nullable();
            $table->decimal('prix_apres', 10, 2)->nullable();
            
            // Informations temporelles
            $table->timestamp('date_action');
            $table->unsignedBigInteger('initie_par')->nullable(); // ID de l'utilisateur qui a initié l'action
            $table->text('raison')->nullable();
            
            // Informations financières
            $table->decimal('montant_paye', 10, 2)->nullable();
            $table->decimal('montant_rembourse', 10, 2)->nullable();
            $table->string('reference_paiement')->nullable();
            $table->string('methode_paiement')->nullable();
            
            // Détails supplémentaires
            $table->json('details_changement')->nullable();
            
            // Tracking
            $table->ipAddress('adresse_ip')->nullable();
            $table->text('user_agent')->nullable();
            $table->json('metadonnees')->nullable();
            
            $table->timestamps();
            
            // Index pour les performances
            $table->index(['marchand_id', 'date_action']);
            $table->index(['abonnement_id']);
            $table->index(['action']);
            $table->index(['initie_par']);
            $table->index(['date_action']);
            $table->index(['reference_paiement']);
            
            // Clés étrangères
            $table->foreign('marchand_id')->references('id')->on('marchands')->onDelete('cascade');
            $table->foreign('abonnement_id')->references('id')->on('marchand_abonnements')->onDelete('set null');
            $table->foreign('initie_par')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marchand_abonnement_historique');
    }
};
