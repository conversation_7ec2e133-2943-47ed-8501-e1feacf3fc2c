<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Nom de la devise (ex: Franc CFA)
            $table->string('code', 3)->unique(); // Code ISO (ex: XAF)
            $table->string('symbol', 10); // Symbole (ex: FCFA)
            $table->decimal('exchange_rate', 10, 4)->default(1.0000); // Taux de change par rapport à la devise de base
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->timestamps();

            $table->index(['is_active']);
            $table->index(['is_default']);
        });

        // Insérer les devises par défaut
        DB::table('currencies')->insert([
            [
                'name' => 'Franc CFA',
                'code' => 'XAF',
                'symbol' => 'FCFA',
                'exchange_rate' => 1.0000,
                'is_active' => true,
                'is_default' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Euro',
                'code' => 'EUR',
                'symbol' => '€',
                'exchange_rate' => 655.957,
                'is_active' => true,
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Dollar US',
                'code' => 'USD',
                'symbol' => '$',
                'exchange_rate' => 600.0000,
                'is_active' => true,
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currencies');
    }
};
