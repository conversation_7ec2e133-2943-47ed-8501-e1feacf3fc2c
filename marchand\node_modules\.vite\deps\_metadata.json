{"hash": "57460100", "configHash": "b20bf04e", "lockfileHash": "cde6c86b", "browserHash": "dc730c78", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "7564e22a", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "61af87a2", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "5fe6b097", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "7643e761", "needsInterop": true}, "@headlessui/react": {"src": "../../@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "5cdb620c", "needsInterop": false}, "@inertiajs/react": {"src": "../../@inertiajs/react/dist/index.esm.js", "file": "@inertiajs_react.js", "fileHash": "00c5bb23", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "78829747", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "8d9399d6", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "25c00449", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "0168e3fa", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "0cdc46c6", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "2ece3919", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "7fd54714", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "6477b39d", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "b800102e", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "45ce26b0", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "83ef707b", "needsInterop": false}, "laravel-vite-plugin/inertia-helpers": {"src": "../../laravel-vite-plugin/inertia-helpers/index.js", "file": "laravel-vite-plugin_inertia-helpers.js", "fileHash": "ef7fc8ba", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "75ee3fd9", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "ded88764", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "71ed04d4", "needsInterop": false}}, "chunks": {"chunk-KHAYJDO5": {"file": "chunk-KHAYJDO5.js"}, "chunk-PJ664RS2": {"file": "chunk-PJ664RS2.js"}, "chunk-EJUMEZYM": {"file": "chunk-EJUMEZYM.js"}, "chunk-SXB54R6U": {"file": "chunk-SXB54R6U.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-4Y43LDCH": {"file": "chunk-4Y43LDCH.js"}, "chunk-4JKHPIC7": {"file": "chunk-4JKHPIC7.js"}, "chunk-7ATFNNHF": {"file": "chunk-7ATFNNHF.js"}, "chunk-CN7RQBZE": {"file": "chunk-CN7RQBZE.js"}, "chunk-7YTMGNOY": {"file": "chunk-7YTMGNOY.js"}, "chunk-PBPI5PFA": {"file": "chunk-PBPI5PFA.js"}, "chunk-5QB6WZBA": {"file": "chunk-5QB6WZBA.js"}, "chunk-EZGML3GP": {"file": "chunk-EZGML3GP.js"}, "chunk-35AGI64M": {"file": "chunk-35AGI64M.js"}, "chunk-Q5B56BVT": {"file": "chunk-Q5B56BVT.js"}, "chunk-3M4ZFO5U": {"file": "chunk-3M4ZFO5U.js"}, "chunk-YJYY6GXC": {"file": "chunk-YJYY6GXC.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}