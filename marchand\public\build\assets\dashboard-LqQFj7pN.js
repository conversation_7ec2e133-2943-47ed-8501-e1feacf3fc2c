import{r as a,j as e,L as o}from"./app-GBhW32uO.js";import{A as l}from"./app-layout-6ShrtIQS.js";import"./app-logo-icon-9xTRfZp0.js";import"./index-CLCtVWy1.js";import"./index-GTcKX9PY.js";function r({className:d}){const s=a.useId();return e.jsxs("svg",{className:d,fill:"none",children:[e.jsx("defs",{children:e.jsx("pattern",{id:s,x:"0",y:"0",width:"10",height:"10",patternUnits:"userSpaceOnUse",children:e.jsx("path",{d:"M-3 13 15-5M-5 5l18-18M-1 21 17 3"})})}),e.jsx("rect",{stroke:"none",fill:`url(#${s})`,width:"100%",height:"100%"})]})}const t=[{title:"Dashboard",href:"/dashboard"}];function h(){return e.jsxs(l,{breadcrumbs:t,children:[e.jsx(o,{title:"Dashboard"}),e.jsxs("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4",children:[e.jsxs("div",{className:"grid auto-rows-min gap-4 md:grid-cols-3",children:[e.jsx("div",{className:"border-sidebar-border/70 dark:border-sidebar-border relative aspect-video overflow-hidden rounded-xl border",children:e.jsx(r,{className:"absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20"})}),e.jsx("div",{className:"border-sidebar-border/70 dark:border-sidebar-border relative aspect-video overflow-hidden rounded-xl border",children:e.jsx(r,{className:"absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20"})}),e.jsx("div",{className:"border-sidebar-border/70 dark:border-sidebar-border relative aspect-video overflow-hidden rounded-xl border",children:e.jsx(r,{className:"absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20"})})]}),e.jsx("div",{className:"border-sidebar-border/70 dark:border-sidebar-border relative min-h-[100vh] flex-1 overflow-hidden rounded-xl border md:min-h-min",children:e.jsx(r,{className:"absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20"})})]})]})}export{h as default};
