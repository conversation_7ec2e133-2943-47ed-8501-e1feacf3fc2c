import{g as p,a as u,r as c,j as f}from"./app-GBhW32uO.js";import{S as d}from"./app-logo-icon-9xTRfZp0.js";var o=u();const E=p(o);var l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],h=l.reduce((t,r)=>{const i=c.forwardRef((s,a)=>{const{asChild:e,...n}=s,m=e?d:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(m,{...n,ref:a})});return i.displayName=`Primitive.${r}`,{...t,[r]:i}},{});function D(t,r){t&&o.flushSync(()=>t.dispatchEvent(r))}export{h as P,E as R,D as d,o as r};
