import { Head, <PERSON> } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Store, TrendingUp, Users, Shield } from 'lucide-react';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
}

export default function Welcome({ user }: Props) {
    const benefits = [
        {
            icon: Store,
            title: "Votre boutique en ligne",
            description: "Créez et gérez facilement votre boutique en ligne avec nos outils intuitifs"
        },
        {
            icon: TrendingUp,
            title: "Augmentez vos ventes",
            description: "Accédez à des milliers de clients potentiels et boostez votre chiffre d'affaires"
        },
        {
            icon: Users,
            title: "Support dédié",
            description: "Bénéficiez d'un accompagnement personnalisé pour développer votre activité"
        },
        {
            icon: Shield,
            title: "Paiements sécurisés",
            description: "Transactions 100% sécurisées avec nos partenaires de paiement certifiés"
        }
    ];

    const steps = [
        "Renseignez vos informations business",
        "Uploadez vos documents d'identification",
        "Validation de votre dossier",
        "Commencez à vendre !"
    ];

    return (
        <>
            <Head title="Bienvenue sur la plateforme marchand" />
            
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
                <div className="container mx-auto px-4 py-8">
                    {/* Header */}
                    <div className="text-center mb-12">
                        <h1 className="text-4xl font-bold text-gray-900 mb-4">
                            Bienvenue sur Lorelei Seller
                        </h1>
                        <p className="text-xl text-gray-600 mb-2">
                            Bonjour {user.name}, prêt à développer votre business ?
                        </p>
                        <p className="text-gray-500">
                            Rejoignez des milliers de marchands qui font confiance à notre plateforme
                        </p>
                    </div>

                    {/* Benefits Section */}
                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                        {benefits.map((benefit, index) => (
                            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                                <CardHeader>
                                    <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                        <benefit.icon className="w-6 h-6 text-blue-600" />
                                    </div>
                                    <CardTitle className="text-lg">{benefit.title}</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <CardDescription>{benefit.description}</CardDescription>
                                </CardContent>
                            </Card>
                        ))}
                    </div>

                    {/* Process Steps */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="text-2xl text-center">
                                Comment ça marche ?
                            </CardTitle>
                            <CardDescription className="text-center">
                                Suivez ces étapes simples pour commencer à vendre
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid md:grid-cols-4 gap-6">
                                {steps.map((step, index) => (
                                    <div key={index} className="text-center">
                                        <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-semibold">
                                            {index + 1}
                                        </div>
                                        <p className="text-sm text-gray-600">{step}</p>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* CTA Section */}
                    <Card className="bg-blue-600 text-white">
                        <CardContent className="text-center py-8">
                            <h2 className="text-2xl font-bold mb-4">
                                Prêt à commencer votre aventure ?
                            </h2>
                            <p className="mb-6 text-blue-100">
                                L'inscription ne prend que quelques minutes. 
                                Commencez dès maintenant et rejoignez notre communauté de marchands.
                            </p>
                            <div className="space-y-4">
                                <Link href={route('seller.business-info')}>
                                    <Button size="lg" variant="secondary" className="w-full md:w-auto">
                                        Commencer l'inscription
                                    </Button>
                                </Link>
                                <div className="flex items-center justify-center space-x-2 text-sm text-blue-100">
                                    <CheckCircle className="w-4 h-4" />
                                    <span>Inscription gratuite</span>
                                    <CheckCircle className="w-4 h-4" />
                                    <span>Support 24/7</span>
                                    <CheckCircle className="w-4 h-4" />
                                    <span>Paiements sécurisés</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Footer */}
                    <div className="text-center mt-8 text-gray-500">
                        <p>
                            Des questions ? Contactez notre équipe support à{' '}
                            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
}
