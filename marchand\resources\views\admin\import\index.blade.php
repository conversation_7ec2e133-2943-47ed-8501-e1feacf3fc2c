<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Importation de données') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    @if (session('success'))
                        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                            {{ session('success') }}

                            @if (session('results'))
                                <div class="mt-2">
                                    <p><strong>Résultats :</strong></p>
                                    <ul class="list-disc ml-5">
                                        <li>Total : {{ session('results')['total'] }}</li>
                                        <li>Créés : {{ session('results')['created'] }}</li>
                                        <li>Mis à jour : {{ session('results')['updated'] }}</li>
                                        @if (!empty(session('results')['errors']))
                                            <li>Erreurs : {{ count(session('results')['errors']) }}</li>
                                            <ul class="list-disc ml-5">
                                                @foreach (session('results')['errors'] as $error)
                                                    <li>{{ $error }}</li>
                                                @endforeach
                                            </ul>
                                        @endif
                                    </ul>
                                </div>
                            @endif
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if ($show_preview)
                        <div class="mb-6 bg-white rounded-lg shadow-md p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Aperçu des catégories à importer</h3>
                                <div>
                                    <p class="text-gray-700">
                                        <span class="font-semibold">Total des catégories :</span> {{ $preview_total }}
                                    </p>
                                </div>
                            </div>

                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white border border-gray-200">
                                    <thead>
                                        <tr>
                                            <th class="px-4 py-2 bg-gray-100 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Ligne
                                            </th>
                                            <th class="px-4 py-2 bg-gray-100 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Nom (FR)
                                            </th>
                                            <th class="px-4 py-2 bg-gray-100 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Nom (EN)
                                            </th>
                                            <th class="px-4 py-2 bg-gray-100 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Catégorie parente
                                            </th>
                                            <th class="px-4 py-2 bg-gray-100 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Statut
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($preview_categories as $category)
                                        <tr class="{{ $category['exists'] ? 'bg-yellow-50' : 'bg-white' }}">
                                            <td class="px-4 py-2 border-b border-gray-200 text-sm">
                                                {{ $category['row_index'] }}
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-200 text-sm">
                                                {{ $category['nom_fr'] }}
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-200 text-sm">
                                                {{ $category['nom_en'] }}
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-200 text-sm">
                                                {{ $category['categorie_parent'] }}
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-200 text-sm">
                                                @if($category['exists'])
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        Existe déjà (similaire à "{{ $category['similar_to'] }}")
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        Nouvelle catégorie
                                                    </span>
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <div class="flex justify-end mt-4">
                                <a href="{{ route('admin.import.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded mr-2">
                                    Annuler
                                </a>
                                <form action="{{ route('admin.import.confirm-categories') }}" method="POST" class="inline">
                                    @csrf
                                    <button type="submit" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded">
                                        Confirmer l'importation
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endif

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Importation de catégories -->
                        <div class="bg-gray-50 p-6 rounded-lg shadow">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Importation de catégories</h3>

                            <form action="{{ route('admin.import.categories') }}" method="POST" enctype="multipart/form-data" class="mb-4">
                                @csrf
                                <div class="mb-4">
                                    <label for="categories_file" class="block text-sm font-medium text-gray-700 mb-2">
                                        Fichier CSV/Excel
                                    </label>
                                    <input type="file" name="file" id="categories_file" class="block w-full text-sm text-gray-500
                                        file:mr-4 file:py-2 file:px-4
                                        file:rounded-md file:border-0
                                        file:text-sm file:font-semibold
                                        file:bg-blue-50 file:text-blue-700
                                        hover:file:bg-blue-100" required>
                                    <p class="mt-1 text-sm text-gray-500">
                                        Formats acceptés : CSV, Excel (.xls, .xlsx)
                                    </p>
                                </div>

                                <div class="flex items-center justify-between">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                        Importer
                                    </button>

                                    <a href="{{ route('admin.import.categories.template') }}" class="text-sm text-blue-600 hover:text-blue-800">
                                        Télécharger le modèle
                                    </a>
                                </div>
                            </form>

                            <div class="mt-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Instructions :</h4>
                                <ul class="list-disc ml-5 text-sm text-gray-600">
                                    <li>Le fichier doit contenir les colonnes suivantes : nom_fr, nom_en, description_fr, description_en, categorie_parent, image_url</li>
                                    <li>Les colonnes nom_fr et nom_en sont obligatoires</li>
                                    <li>Pour créer une sous-catégorie, indiquez le nom de la catégorie parente dans la colonne categorie_parent</li>
                                    <li>L'image_url doit être un chemin relatif ou une URL</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Importation de produits -->
                        <div class="bg-gray-50 p-6 rounded-lg shadow">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Importation de produits</h3>

                            <form action="{{ route('admin.import.products') }}" method="POST" enctype="multipart/form-data" class="mb-4">
                                @csrf
                                <div class="mb-4">
                                    <label for="products_file" class="block text-sm font-medium text-gray-700 mb-2">
                                        Fichier CSV/Excel
                                    </label>
                                    <input type="file" name="file" id="products_file" class="block w-full text-sm text-gray-500
                                        file:mr-4 file:py-2 file:px-4
                                        file:rounded-md file:border-0
                                        file:text-sm file:font-semibold
                                        file:bg-blue-50 file:text-blue-700
                                        hover:file:bg-blue-100" required>
                                    <p class="mt-1 text-sm text-gray-500">
                                        Formats acceptés : CSV, Excel (.xls, .xlsx)
                                    </p>
                                </div>

                                <div class="mb-4">
                                    <label for="marchand_id" class="block text-sm font-medium text-gray-700 mb-2">
                                        Marchand (optionnel)
                                    </label>
                                    <select name="marchand_id" id="marchand_id" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">Sélectionner un marchand</option>
                                        @foreach (\App\Models\Marchand::all() as $marchand)
                                            <option value="{{ $marchand->id }}">{{ $marchand->nomEntreprise }}</option>
                                        @endforeach
                                    </select>
                                    <p class="mt-1 text-sm text-gray-500">
                                        Si non sélectionné, le marchand connecté ou le premier marchand disponible sera utilisé
                                    </p>
                                </div>

                                <div class="flex items-center justify-between">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                        Importer
                                    </button>

                                    <a href="{{ route('admin.import.products.template') }}" class="text-sm text-blue-600 hover:text-blue-800">
                                        Télécharger le modèle
                                    </a>
                                </div>
                            </form>

                            <div class="mt-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Instructions :</h4>
                                <ul class="list-disc ml-5 text-sm text-gray-600">
                                    <li>Le fichier doit contenir les colonnes suivantes : nom_fr, nom_en, description_fr, description_en, categorie, prix, currency, stock, prix_remise, date_debut_remise, date_fin_remise, poids, longueur, largeur, hauteur, product_code, marque</li>
                                    <li>Les colonnes nom_fr, categorie et prix sont obligatoires</li>
                                    <li>La catégorie doit correspondre à une catégorie existante (nom en français)</li>
                                    <li>La colonne currency permet de spécifier la devise (FCFA, EUR, USD, GBP, XAF, XOF). Si non spécifiée, FCFA sera utilisée par défaut</li>
                                    <li>Les codes de devise sont automatiquement convertis en majuscules lors de l'importation</li>
                                    <li>Les dates doivent être au format YYYY-MM-DD</li>
                                    <li>Les images devront être ajoutées manuellement après l'importation</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
