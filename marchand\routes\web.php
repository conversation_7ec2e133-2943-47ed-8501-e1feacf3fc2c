<?php

use App\Http\Controllers\SellerRegistrationController;
use App\Http\Controllers\SellerDashboardController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Web Routes - Plateforme Marchand (seller.lorelei.com)
|--------------------------------------------------------------------------
*/

// Page d'accueil pour les marchands
Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

/*
|--------------------------------------------------------------------------
| Routes d'inscription marchand
|--------------------------------------------------------------------------
*/
Route::middleware(['auth'])->prefix('seller')->name('seller.')->group(function () {

    // Processus d'inscription
    Route::get('/welcome', [SellerRegistrationController::class, 'welcome'])->name('welcome');
    Route::get('/business-info', [SellerRegistrationController::class, 'businessInfo'])->name('business-info');
    Route::post('/business-info', [SellerRegistrationController::class, 'storeBusinessInfo'])->name('business-info.store');
    Route::get('/documents', [SellerRegistrationController::class, 'documents'])->name('documents');
    Route::post('/documents/upload', [SellerRegistrationController::class, 'uploadDocument'])->name('documents.upload');
    Route::get('/finalize', [SellerRegistrationController::class, 'finalize'])->name('finalize');

});

/*
|--------------------------------------------------------------------------
| Routes du dashboard marchand
|--------------------------------------------------------------------------
*/
Route::middleware(['auth', 'seller'])->prefix('dashboard')->name('dashboard.')->group(function () {

    // Dashboard principal
    Route::get('/', [SellerDashboardController::class, 'index'])->name('index');

    // Profil marchand
    Route::get('/profile', [SellerDashboardController::class, 'profile'])->name('profile');
    Route::patch('/profile', [SellerDashboardController::class, 'updateProfile'])->name('profile.update');

    // Abonnements
    Route::get('/subscriptions', [SellerDashboardController::class, 'subscriptions'])->name('subscriptions');

    // Documents
    Route::get('/documents', [SellerDashboardController::class, 'documents'])->name('documents');

});

// Redirection du dashboard par défaut vers le dashboard marchand
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return redirect()->route('dashboard.index');
    })->name('dashboard');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
